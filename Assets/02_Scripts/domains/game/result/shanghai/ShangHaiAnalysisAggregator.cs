using System.Collections.Generic;
using System.Linq;
using com.luxza.grandarts.domains.game.result.shanghai;

namespace com.luxza.grandarts.domains.game.result.multiplecr
{
    public static class ShangHaiAnalysisAggregator {
        public static ShangHaiAnalysisData Aggregate(IEnumerable<ShangHaiAnalysisData> analysisDatas) {
            var playerCount = analysisDatas.Count();
            if(playerCount == 0) return new ShangHaiAnalysisData();

            var mergedAwardCounts = analysisDatas.SelectMany(p => p.AwardCounts)
                .GroupBy(award => award.Key)
                .ToDictionary(
                    award => award.Key,
                    award => award.Sum(kvp => kvp.Value)
                );

            return new ShangHaiAnalysisData(
                sBullCount: analysisDatas.Sum(data => data.SBullCount),
                dBullCount: analysisDatas.Sum(data => data.DBullCount),
                t20Count: analysisDatas.Sum(data => data.T20Count),
                awardCounts: mergedAwardCounts
            );
        }
    }
}