using System;
using com.luxza.grandartslogic.domain.game;
using com.luxza.unitygraphqlclient.gen;

namespace com.luxza.grandarts.infrastructures.graphql.converter {
    public static class GameCodeConverter {
        internal static Types.GameCodeV2 ToGraphQLGameCode(this GameCode? code)
        {
            if(code == null) {
                return Types.GameCodeV2.CHOICE;
            }
            return ToGraphQLGameCode(code.Value);
        }
        internal static Types.OnlineMatchUpGameCode ToOnlineMatchUpGameCode(this GameCode? code)
        {
            if (code == null)
            {
                return Types.OnlineMatchUpGameCode.Choice;
            }
            return ToOnlineMatchUpGameCode(code.Value);
        }

        internal static Types.OnlineMatchUpGameCode ToOnlineMatchUpGameCode(this GameCode code)
        {
            return code switch
            {
                GameCode._301 => Types.OnlineMatchUpGameCode.ZeroOne301,
                GameCode._501 => Types.OnlineMatchUpGameCode.ZeroOne501,
                GameCode._701 => Types.OnlineMatchUpGameCode.ZeroOne701,
                GameCode._StandardCR => Types.OnlineMatchUpGameCode.Cricket,
                _ => throw new NotSupportedException($"GameCode {code} is not supported."),
            };
        }
        internal static Types.GameCodeV2 ToGraphQLGameCode(this GameCode code)
        {
            return code switch
            {
                GameCode._301 => Types.GameCodeV2.ZERO_ONE,
                GameCode._501 => Types.GameCodeV2.ZERO_ONE,
                GameCode._701 => Types.GameCodeV2.ZERO_ONE,
                GameCode._901 => Types.GameCodeV2.ZERO_ONE,
                GameCode._1101 => Types.GameCodeV2.ZERO_ONE,
                GameCode._1501 => Types.GameCodeV2.ZERO_ONE,
                GameCode._Freeze301 => Types.GameCodeV2.FREEZE_ZERO_ONE,
                GameCode._Freeze501 => Types.GameCodeV2.FREEZE_ZERO_ONE,
                GameCode._Freeze701 => Types.GameCodeV2.FREEZE_ZERO_ONE,
                GameCode._Freeze901 => Types.GameCodeV2.FREEZE_ZERO_ONE,
                GameCode._Freeze1101 => Types.GameCodeV2.FREEZE_ZERO_ONE,
                GameCode._Freeze1501 => Types.GameCodeV2.FREEZE_ZERO_ONE,
                GameCode._KickDown301 => Types.GameCodeV2.KICK_DOWN,
                GameCode._KickDown501 => Types.GameCodeV2.KICK_DOWN,
                GameCode._StandardCR => Types.GameCodeV2.CRICKET,
                GameCode._HiddenCR => Types.GameCodeV2.HIDDEN_CRICKET,
                GameCode._CutThroat => Types.GameCodeV2.CUT_THROAT_CRICKET,
                GameCode._HiddenCutThroat => Types.GameCodeV2.HIDDEN_CUT_THROAT_CRICKET,
                GameCode._TeamCR => Types.GameCodeV2.TEAM_CRICKET,
                GameCode._WildCardCR => Types.GameCodeV2.WILD_CARD_CRICKET,
                GameCode._Countup => Types.GameCodeV2.COUNT_UP,
                GameCode._CrCountUp => Types.GameCodeV2.CRICKET_COUNT_UP,
                GameCode._HalfIt => Types.GameCodeV2.HALF_IT,
                GameCode._DeltaShoot => Types.GameCodeV2.DELTA_SHOOT,
                GameCode._TargetHat => Types.GameCodeV2.TARGET_HAT,
                GameCode._TargetChoice => throw new NotSupportedException($"GameCode {code} is not supported."),
                GameCode._Oniren => Types.GameCodeV2.ONIREN,
                GameCode._TargetBull => Types.GameCodeV2.TARGET_BULL,
                GameCode._Target20 => Types.GameCodeV2.TARGET_TWENTY,
                GameCode._ShootForce => Types.GameCodeV2.SHOOT_FORCE,
                GameCode._TargetHorse => Types.GameCodeV2.TARGET_HORSE,
                GameCode._MultipleCr => Types.GameCodeV2.MULTIPLE_CRICKET,
                GameCode._Pirates => Types.GameCodeV2.PIRATES,
                GameCode._Spider => Types.GameCodeV2.SPIDER,
                GameCode._Rotation => Types.GameCodeV2.ROTATION,
                GameCode._BeyondTop => Types.GameCodeV2.BEYOND_TOP,
                GameCode._HyperBull => throw new NotSupportedException($"GameCode {code} is not supported."),
                GameCode._TreasureHunt => throw new NotSupportedException($"GameCode {code} is not supported."),
                GameCode._2Line => throw new NotSupportedException($"GameCode {code} is not supported."),
                GameCode._HideAndSeek => throw new NotSupportedException($"GameCode {code} is not supported."),
                GameCode._FunMission => throw new NotSupportedException($"GameCode {code} is not supported."),
                GameCode._TicTacToe => throw new NotSupportedException($"GameCode {code} is not supported."),
                GameCode._ANIMAL_301GAME => Types.GameCodeV2.ZERO_ONE,
                GameCode._ANIMAL_501GAME => Types.GameCodeV2.ZERO_ONE,
                GameCode._ANIMAL_701GAME => Types.GameCodeV2.ZERO_ONE,
                GameCode._ANIMAL_901GAME => Types.GameCodeV2.ZERO_ONE,
                GameCode._ANIMAL_1101GAME => Types.GameCodeV2.ZERO_ONE,
                GameCode._ANIMAL_1501GAME => Types.GameCodeV2.ZERO_ONE,
                GameCode._ANIMAL_STANDARD_CR => Types.GameCodeV2.CRICKET,
                GameCode._BaseBall => throw new NotSupportedException($"GameCode {code} is not supported."),
                GameCode._ShangHai => Types.GameCodeV2.SHANGHAI
            };
        }
    }
}