using TMPro;
using LitMotion;
using UnityEngine;

namespace com.luxza.grandarts.userinterfaces.components.game.play.local.shanghai
{
    public class HalfEffect : MonoBehaviour
    {
        [SerializeField] private AudioSource _halfSE;
        [SerializeField] private GameObject _effect;
        // [SerializeField] private Image _skullImage;
        [SerializeField] private TextMeshProUGUI _txt_halfIt;
        public void Init()
        {
            Reset();
        }

        public void Play()
        {
            Reset();
            _halfSE.Play();
            LMotion.Create(0.0f, 1.0f, 1.0f)
                   .WithEase(Ease.OutCubic)
                   .WithOnComplete(() =>
                    {
                        // _skullImage.color = new Color(_skullImage.color.r, _skullImage.color.g, _skullImage.color.b, 1);
                        _txt_halfIt.color = new Color(_txt_halfIt.color.r, _txt_halfIt.color.g, _txt_halfIt.color.b, 1);
                        LMotion.Create(0.0f, 1.0f, 0.3f).WithOnComplete(() =>
                        {
                            Reset();
                        }).Bind(v=> { }).AddTo(gameObject);
                    })
                    .Bind(v =>
                    {
                        _effect.SetActive(true);
                        // _skullImage.color = new Color(_skullImage.color.r, _skullImage.color.g, _skullImage.color.b, v);
                        _txt_halfIt.color = new Color(_txt_halfIt.color.r, _txt_halfIt.color.g, _txt_halfIt.color.b, v);
                    })
                    .AddTo(gameObject);
        }

        public void Stop()
        {
            Reset();
        }


        private void Reset()
        {
            _halfSE.Stop();
            _effect.SetActive(false);
            // _skullImage.color = new Color(_skullImage.color.r, _skullImage.color.g, _skullImage.color.b, 0);
            _txt_halfIt.color = new Color(_txt_halfIt.color.r, _txt_halfIt.color.g, _txt_halfIt.color.b, 0);
        }
    }
}
