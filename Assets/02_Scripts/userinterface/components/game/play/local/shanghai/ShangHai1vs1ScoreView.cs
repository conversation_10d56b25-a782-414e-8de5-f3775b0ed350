using System;
using System.Linq;
using com.luxza.grandarts.domains.game.context.shanghai;
using com.luxza.grandarts.domains.game.unit;
using com.luxza.granlog;
using com.luxza.ui.components.atoms;
using R3;
using UnityEngine;
using UnityEngine.UI;
using com.luxza.grandarts.userinterfaces.components.common.animation;
using com.luxza.grandarts.userinterfaces.game.context;
using com.luxza.grandartslogic.domain.game.shanghai;

namespace com.luxza.grandarts.userinterfaces.components.game.play.local.shanghai
{
    [DisallowMultipleComponent]
    public class ShangHai1vs1ScoreView : Base1vs1ScoreView
    {
        [SerializeField] private Image _unitColorLeft;
        [SerializeField] private GameObject _leftYourTurnObject;
        [SerializeField] private Image _unitColorRight;
        [SerializeField] private GameObject _rightYourTurnObject;
        [SerializeField] private GranText _gameFormatText;
        [SerializeField] private UnitIconsWithScoreListView _unitIcons;
        [SerializeField] private ShangHaiGameContextAdoptor _adoptor;
        [SerializeField] private GranText _roundText;
        [SerializeField] private DigitAnimationController _leftDigitController;
        [SerializeField] private DigitAnimationController _rightDigitController;
        [Header("ShangHai"),SerializeField] private HalfEffect _halfEffect;
        [SerializeField] private GameObject _shangHaiEffect;

        private PlayUnit[] _playUnits;
        private PlayUnit LeftUnit => _playUnits[0];
        private PlayUnit RightUnit => _playUnits[1];
        private GameShangHaiSessionContext _context;
        private GameRuleShangHai _rule;
        private int _currentDisplayedScoreLeft;
        private int _currentDisplayedScoreRight;

        private void Awake() {
            _adoptor.OnReady.Subscribe(context =>
            {
                _context = context as GameShangHaiSessionContext;
                if (_context == null)
                {
                    throw new ArgumentException("ShangHai context is null.");
                }

                Bind(context.DataRetriver.BasicGameDataRetriver.PlayUnits.ToArray(), _context.DataRetriver.GameRule);
                _context.GameEventPublisher.OnStartMatch += OnStartMatch;
                _context.GameEventPublisher.OnUpdateProgress += (_) => OnProgressUpdated();
                _context.GameEventPublisher.OnRoundReverse += (_) => OnProgressUpdated();
                _context.GameEventPublisher.OnThrowReverse += (_) => OnProgressUpdated();
                _context.GameEventPublisher.OnChange += (_) => OnThrowerChanged();
                _context.GameEventPublisher.OnOverrideThrow += (_, _) => OnProgressUpdated();
                _context.GameEventPublisher.OnEndTurn += (_) => OnEndTurn();
                _context.GameEventPublisher.OnFinishMatch += (_) => OnGameFinished();
            }).RegisterTo(destroyCancellationToken);

            ResetView("", "", false, false);
        }

        private void AnimateScoreChange(int newScore, bool isLeft)
        {
            if (isLeft)
            {
                if (newScore == _currentDisplayedScoreLeft)
                {
                    return;
                }

                _currentDisplayedScoreLeft = newScore;
                if (_leftDigitController != null)
                {
                    _leftDigitController.AnimateNumber(newScore, DigitAnimationController.AnimationType.FallWithBounce);
                }
            }
            else
            {
                if (newScore == _currentDisplayedScoreRight)
                {
                    return;
                }

                _currentDisplayedScoreRight = newScore;
                if (_rightDigitController != null)
                {
                    _rightDigitController.AnimateNumber(newScore, DigitAnimationController.AnimationType.FallWithBounce);
                }
            }
        }

        private void ResetView(string leftScore, string rightScore,bool isLeftUnit,bool isRightUnit)
        {
            int.TryParse(leftScore, out _currentDisplayedScoreLeft);
            int.TryParse(rightScore, out _currentDisplayedScoreRight);

            if (!string.IsNullOrEmpty(leftScore))
            {
                if (_leftDigitController != null)
                {
                    _leftDigitController.SetNumber(_currentDisplayedScoreLeft);
                }
            }

            if (!string.IsNullOrEmpty(rightScore))
            {
                if (_rightDigitController != null)
                {
                    _rightDigitController.SetNumber(_currentDisplayedScoreRight);
                }
            }

            _leftYourTurnObject.SetActive(isLeftUnit);
            _rightYourTurnObject.SetActive(isRightUnit);
            if (isLeftUnit)
            {
                UnitColorActiveAnimation(_unitColorLeft);
            }
            else
            {
                _unitColorLeft.color = UnitColors.NonActivePlayerColor;
            }
            if (isRightUnit)
            {
                UnitColorActiveAnimation(_unitColorRight);
            }
            else
            {
                _unitColorRight.color = UnitColors.NonActivePlayerColor;
            }
        }

        public void Bind(PlayUnit[] units, GameRuleShangHai rule)
        {
            if (units.Length != 2)
            {
                throw new ArgumentException("This view required 2 units.");
            }
            _playUnits = units;
            _rule = rule;
            _unitIcons.Bind(units, showScore: false);
            _gameFormatText.text = GameFormatUtility.GameNameText(rule.Code);
        }

        private void OnStartMatch()
        {
            Log.i("Start match.");
            ResetView(_context.DataRetriver.BasicGameDataRetriver.Score(LeftUnit.Id).ToString(),
                      _context.DataRetriver.BasicGameDataRetriver.Score(RightUnit.Id).ToString(),
                      _context.DataRetriver.BasicGameDataRetriver.CurrentThrowingUnitId == LeftUnit.Id,
                      _context.DataRetriver.BasicGameDataRetriver.CurrentThrowingUnitId == RightUnit.Id);
            _unitIcons.UpdateThrower(LeftUnit.Members[0].Id);

            _unitIcons.UpdateThrower(LeftUnit.Members[0].Id);
            _roundText.text = $"1 / {_rule.MaxRound}";
        }

        private void OnProgressUpdated() {
            RefreshScoreView();
        }
        
        private void OnEndTurn()
        {
            if (_context.DataRetriver.CurrentUnitHasHalfByCurrentRound)
            {
                _halfEffect.Play();
                _unitIcons.UpdateScores(_context.DataRetriver.BasicGameDataRetriver.ScoresOfAllUnits);
            }
        }

        private void OnGameFinished()
        {
            _shangHaiEffect.SetActive(true);
        }

        private void OnThrowerChanged()
        {
            var playerId = _context.DataRetriver.BasicGameDataRetriver.CurrentThrower.Id;
            _unitIcons.UpdateThrower(playerId);
            RefreshScoreView();
            _roundText.text = $"{_context.DataRetriver.BasicGameDataRetriver.CurrentRound.No} / {_rule.MaxRound}";
            if (LeftUnit.IsMember(playerId))
            {
                UnitColorActiveAnimation(_unitColorLeft);
                _unitColorRight.color = UnitColors.NonActivePlayerColor;
                _leftYourTurnObject.SetActive(true);
                _rightYourTurnObject.SetActive(false);
            }
            else if (RightUnit.IsMember(playerId))
            {
                _unitColorLeft.color = UnitColors.NonActivePlayerColor;
                UnitColorActiveAnimation(_unitColorRight);
                _leftYourTurnObject.SetActive(false);
                _rightYourTurnObject.SetActive(true);
            }
            else
            {
                throw new ArgumentException($"All units has not member: {playerId}");
            }
        }

        private void RefreshScoreView()
        {
            int leftScore = _context.DataRetriver.BasicGameDataRetriver.Score(LeftUnit.Id);
            int rightScore = _context.DataRetriver.BasicGameDataRetriver.Score(RightUnit.Id);

            AnimateScoreChange(leftScore, true);
            AnimateScoreChange(rightScore, false);

            _unitIcons.UpdateScores(_context.DataRetriver.BasicGameDataRetriver.ScoresOfAllUnits);
        }

    }
}
