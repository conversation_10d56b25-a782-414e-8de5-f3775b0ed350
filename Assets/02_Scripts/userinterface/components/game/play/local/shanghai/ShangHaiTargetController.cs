using System;
using com.luxza.grandarts.domains.game;
using com.luxza.grandarts.userinterfaces.game.context;
using R3;
using UnityEngine;
using UnityEngine.UI;

namespace com.luxza.grandarts.userinterfaces.components.game.play.local.shanghai
{
    public class ShangHaiTargetController : MonoBehaviour
    {
        [SerializeField] private BasicGameContextAdoptor _adoptor;
        [SerializeField] private GameObject[] _throwAtList;
        [SerializeField] private GameObject[] _obj_AreaList;
        
        private IGameSessionContext _context;
        
        private void Awake() {
            _adoptor.OnReady.Subscribe(context =>
            {
                _context = context;
                if (_context == null)
                {
                    throw new ArgumentException("Rotation context is null.");
                }
                _context.GameEventPublisher.OnStartMatch += OnStartMatch;
                _context.GameEventPublisher.OnUpdateProgress += (_) => OnProgressUpdated();
                _context.GameEventPublisher.OnChange += (_) => OnThrowerChanged();
                _context.GameEventPublisher.OnRoundReverse += OnRoundReverse;
                _context.GameEventPublisher.OnOverrideThrow += (_, _) => OnProgressUpdated();
                _context.GameEventPublisher.OnRoundReverse += (_) => OnProgressUpdated();
                _context.GameEventPublisher.OnThrowReverse += (_) => OnProgressUpdated();
            }).RegisterTo(destroyCancellationToken);
        }
        
        private void OnStartMatch()
        {
            this.ShowTargetForCurrentRound();
        }

        private void OnProgressUpdated()
        {
            
        }

        private void OnThrowerChanged()
        {
            this.ShowTargetForCurrentRound();
        }
        
        private void OnRoundReverse(grandartslogic.Unit unit)
        {
            this.ShowTargetForCurrentRound();
        }
        
        private void ShowTargetForCurrentRound()
        {
            var num = _context.DataRetriver.BasicGameDataRetriver.CurrentRound.No;
            for (int i = 0; i < _obj_AreaList.Length; i++)
            {
                _obj_AreaList[i].SetActive(num - 1 == i);
                _throwAtList[i].SetActive(num - 1 == i);
                if (num - 1 == i)
                {
                    var tList = _obj_AreaList[i].GetComponentsInChildren<Image>(true);
                    for (int j = 0; j < tList.Length; j++)
                    {
                        tList[j].gameObject.SetActive(true);
                    }
                }
            }
        }
    }
}