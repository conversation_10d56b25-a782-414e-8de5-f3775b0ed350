using com.luxza.grandarts.domains.game.setting;
using com.luxza.grandarts.usecases.auth;
using com.luxza.ui.components.atoms;
using Cysharp.Threading.Tasks;
using UnityEngine;
using R3;
using com.luxza.grandarts.domains.game.format;
using System;
using com.luxza.grandarts.userinterfaces.components.game.play;
using com.luxza.grandarts.usecases.game.setting;
using com.luxza.grandarts.dicontainer;
using com.luxza.grandarts.utils;
using com.luxza.granlog;

namespace com.luxza.grandarts.userinterfaces.components.game.setting.shanghai
{
    public class LocalShangHaiOptionView : MonoBehaviour
    {
        [SerializeField] GranToggle _playUntilTheEndToggle;
        [SerializeField] GranToggle _maxRound7Toggle;
        [SerializeField] GranToggle _maxRound10Toggle;
        [SerializeField] GranToggle _maxRound20Toggle;
        [SerializeField] private GranToggle[] _halfRoundToggleList;
        [SerializeField] private GameObject[] _halfRoundLines;
        [SerializeField] GameObject _loadingObject;
        [SerializeField] CanvasGroup _canvasGroup;
        [SerializeField] GranText _requestFormatText;

        public ShangHaiRequestFormat RequestFormat { get; private set; }

        private async void Awake()
        {
            await UniTask.WaitUntil(() => ApplicationAuth.IsLoggedIn);
            await LoadFormatAsync();

            _playUntilTheEndToggle.onValueChangedAsObservable.Subscribe(isOn =>
            {
                RequestFormat.IsPlayUntilMaxRound = isOn;
                _requestFormatText.text = GameFormatUtility.GameFormatTextWithOptions(RequestFormat);
                UpdateHalfRoundToggles();
            }).RegisterTo(destroyCancellationToken);
            
            _maxRound7Toggle.onValueChangedAsObservable.Subscribe(isOn =>
            {
                if (!isOn) return;
                RequestFormat.MaxRound = new MaxRound(7);
                _requestFormatText.text = GameFormatUtility.GameFormatTextWithOptions(RequestFormat);
                UpdateHalfRoundToggles();
            }).RegisterTo(destroyCancellationToken);
            
            _maxRound10Toggle.onValueChangedAsObservable.Subscribe(isOn =>
            {
                if (!isOn) return;
                RequestFormat.MaxRound = new MaxRound(10);
                _requestFormatText.text = GameFormatUtility.GameFormatTextWithOptions(RequestFormat);
                UpdateHalfRoundToggles();
            }).RegisterTo(destroyCancellationToken);
            
            _maxRound20Toggle.onValueChangedAsObservable.Subscribe(isOn =>
            {
                if (!isOn) return;
                RequestFormat.MaxRound = new MaxRound(20);
                _requestFormatText.text = GameFormatUtility.GameFormatTextWithOptions(RequestFormat);
                UpdateHalfRoundToggles();
            }).RegisterTo(destroyCancellationToken);

            for (int i = 0; i < _halfRoundToggleList.Length; i++)
            {
                var id = i;
                _halfRoundToggleList[id].onValueChangedAsObservable.Subscribe(isOn =>
                {
                    RequestFormat.HalfRoundOption[id] = isOn ? 1 : 0;
                    _requestFormatText.text = GameFormatUtility.GameFormatTextWithOptions(RequestFormat);
                    Log.d($"HalfRoundList,{RequestFormat.HalfRoundToString()}");
                }).RegisterTo(destroyCancellationToken);
            }
        }

        private async UniTask LoadFormatAsync() {
            Loading(true);
            try {
                await UniTask.Delay(TimeSpan.FromSeconds(1));
                var usecase = new GetShangHaiPlayFormatUsecase(DIContainer.Instance.Resolve<ILocalGameRequestFormatRepository>());
                var format = await usecase.ExecuteAsync(ApplicationAuth.LoggedInUser.CurrentPlayer, destroyCancellationToken);
                RequestFormat = format.RequestFormat;
                _requestFormatText.text = GameFormatUtility.GameFormatTextWithOptions(RequestFormat);
                _playUntilTheEndToggle.SetIsOnWithoutNotify(RequestFormat.IsPlayUntilMaxRound);
                _maxRound7Toggle.SetIsOnWithoutNotify(RequestFormat.MaxRound == new MaxRound(7));
                _maxRound10Toggle.SetIsOnWithoutNotify(RequestFormat.MaxRound == new MaxRound(10));
                _maxRound20Toggle.SetIsOnWithoutNotify(RequestFormat.MaxRound == new MaxRound(20));

                UpdateHalfRoundToggles();
            } finally {
                Loading(false);
            }
        }

        private void Loading(bool isLoading) {
            _loadingObject.SetActive(isLoading);
            _canvasGroup.alpha = isLoading ? 0.5f : 1f;
            _canvasGroup.interactable = !isLoading;
        }

        private void UpdateHalfRoundToggles()
        {
            for (int i = 0; i < _halfRoundToggleList.Length; i++)
            {
                var id = i;
                _halfRoundToggleList[id].gameObject.SetActive(RequestFormat.MaxRound > id);
                if (RequestFormat.MaxRound > id)
                {
                    _halfRoundToggleList[id].SetIsOnWithoutNotify(RequestFormat.HalfRoundOption[id] == 1);
                }
            }

            var lineCount = 4;
            switch (RequestFormat.MaxRound)
            {
                case 7:
                case 10:
                    lineCount = 2;
                    break;
                case 20:
                    lineCount = 4;
                    break;
            }

            for (int i = 0; i < _halfRoundLines.Length; i++)
            {
                _halfRoundLines[i].SetActive(i < lineCount);
            }
        }

        private void OnDestroy()
        {
            new SaveShangHaiRequestFormatUsecase(DIContainer.Instance.Resolve<ILocalGameRequestFormatRepository>())
                .ExecuteAsync(ApplicationAuth.LoggedInUser.CurrentPlayer.Id, RequestFormat, destroyCancellationToken)
                .SafeForget();
        }
    }
}