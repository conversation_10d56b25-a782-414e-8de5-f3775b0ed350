using R3;
using System;
using UnityEngine;
using System.Linq;
using com.luxza.ui.page;
using UnityEngine.UI;
using com.luxza.ui.components.atoms;
using com.luxza.ui.components.molecules;
using com.luxza.grandarts.userinterfaces.page.parameters;
using com.luxza.grandarts.userinterfaces.components.result;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandarts.userinterfaces.page.utils;
using com.luxza.grandarts.usecases.game;
using com.luxza.grandarts.dicontainer;
using com.luxza.grandarts.domains.game.format;
using com.luxza.grandartslogic.domain.game.zeroone;
using com.luxza.grandarts.domains.game.unit;
using com.luxza.grandarts.domains.game.result;
using com.luxza.grandarts.domains.game.setting;
using com.luxza.grandarts.domains.game.result.zeroone;
using com.luxza.grandarts.domains.game.result.standardcr;
using com.luxza.grandarts.usecases.analysis;
using com.luxza.grandarts.userinterfaces.components.game.play;



namespace com.luxza.grandarts.userinterfaces.page.result
{
    public class PageGameResult1VS1View : MonoBehaviour
    {
        [SerializeField] private Page _page;
        [SerializeField] private GranText _title;
        [SerializeField] private GameSetLeg _setLeft;
        [SerializeField] private GameSetLeg _legLeft;
        [SerializeField] private GameSetLeg _setRight;
        [SerializeField] private GameSetLeg _legRight;
        [SerializeField] private GameResultUnitIcon _leftUnitIcon;
        [SerializeField] private GameResultUnitIcon _rightUnitIcon;
        [SerializeField] private GranButton _btn_GameEnd;
        [SerializeField] private GranButton _btn_PlayAgain;
        [SerializeField] private GameResultData1vs1Item _GameResultDataItemPrefab;
        [SerializeField] private ScrollRect _GameResultData1v1Scroll;

        [SerializeField] private GameObject _devider01;
        [SerializeField] private GameObject _deviderCR;


        void Awake()
        {
            foreach (Transform item in _GameResultData1v1Scroll.content.transform) Destroy(item.gameObject);
            _setLeft.Hide();
            _legLeft.Hide();
            _setRight.Hide();
            _legRight.Hide();
        }

        private void Start()
        {
            _page.OnActivate.Subscribe(OnActivate).RegisterTo(destroyCancellationToken);
        }

        private void OnActivate(Unit unit)
        {
            if (_page.Meta.TryGetParameter<GameResultParameter>(out var param))
            {
                Init(param);
                _btn_GameEnd.onClickAsObservable.Subscribe(_ => OnClickGameEnd()).RegisterTo(destroyCancellationToken);
            }
            else
            {
                throw new ArgumentException("Invalid parameter.");
            }
        }

        private void Init(GameResultParameter param)
        {
            var results = param.GetLatestGameResultsByUnit().ToArray();
            if(results.Count() != 2) throw new ArgumentException("Invalid results length.");
            if (param.TryGetx01MatchProgress(out var x01Progress))
            {
                _title.text = $"{x01Progress.PlayFormat.RequestFormat.GameCode.LongName()}";
                ShowX01ResultData(results[0], results[1], x01Progress.Winner, x01Progress.PlayFormat.RequestFormat.OutCondition);
                _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(x01Progress.PlayFormat)).RegisterTo(destroyCancellationToken);
            }
            else if (param.TryGetStandardCRMatchProgress(out var crProgress))
            {
                _title.text = $"{crProgress.PlayFormat.RequestFormat.GameCode.LongName()}";
                if (crProgress.PlayFormat.RequestFormat.GameCode == GameCode._StandardCR)
                {
                    ShowStandardCrResultData(results[0], results[1], crProgress.Winner);
                }
                else
                {
                    ShowHiddenCrResultData(results[0], results[1], crProgress.Winner);
                }
                _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(crProgress.PlayFormat)).RegisterTo(destroyCancellationToken);
            }
            else if (param.TryGetCountUpProgress(out var countUpProgress))
            {
                _title.text = $"{countUpProgress.PlayFormat.RequestFormat.GameCode.LongName()}";
                ShowCountUpResultData(results[0], results[1], countUpProgress.Winner);
                _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(countUpProgress.PlayFormat)).RegisterTo(destroyCancellationToken);
            }
            else if (param.TryGetTarget20Progress(out var target20Progress))
            {
                _title.text = $"{target20Progress.PlayFormat.RequestFormat.GameCode.LongName()}";
                ShowTarget20ResultData(results[0], results[1], target20Progress.Winner);
                _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(target20Progress.PlayFormat)).RegisterTo(destroyCancellationToken);
            }
            else if (param.TryGetHalfItProgress(out var halfItProgress))
            {
                _title.text = $"{halfItProgress.PlayFormat.RequestFormat.GameCode.LongName()}";
                ShowHalfItResultData(results[0], results[1], halfItProgress.Winner);
                _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(halfItProgress.PlayFormat)).RegisterTo(destroyCancellationToken);
            }
            else if (param.TryGetRotationProgress(out var rotationProgress))
            {
                _title.text = $"{rotationProgress.PlayFormat.RequestFormat.GameCode.LongName()}";
                ShowRotationResultData(results[0], results[1], rotationProgress.Winner);
                _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(rotationProgress.PlayFormat)).RegisterTo(destroyCancellationToken);
            }
            else if (param.TryGetKickdownProgress(out var kickDownProgress))
            {
                _title.text = $"{kickDownProgress.PlayFormat.RequestFormat.GameCode.LongName()}";
                ShowKickdownResultData(results[0], results[1], kickDownProgress.Winner);
                _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(kickDownProgress.PlayFormat)).RegisterTo(destroyCancellationToken);
            }
            else if (param.TryGetBeyondTopProgress(out var beyondTopProgress))
            {
                _title.text = $"{beyondTopProgress.PlayFormat.RequestFormat.GameCode.LongName()}";
                ShowBeyondTopResultData(results[0], results[1], beyondTopProgress.Winner);
                _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(beyondTopProgress.PlayFormat)).RegisterTo(destroyCancellationToken);
            }
            else if (param.TryGetMultipleCRProgress(out var multipleCRProgress))
            {
                _title.text = $"{multipleCRProgress.PlayFormat.RequestFormat.GameCode.LongName()}";
                ShowMultipleCRResultData(results[0], results[1], multipleCRProgress.Winner);
                _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(multipleCRProgress.PlayFormat)).RegisterTo(destroyCancellationToken);
            }
            else if (param.TryGetShangHaiProgress(out var shangHaiProgress))
            {
                _title.text = $"{shangHaiProgress.PlayFormat.RequestFormat.GameCode.LongName()}";
                ShowShangHaiResultData(results[0], results[1], shangHaiProgress.Winner);
                _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(shangHaiProgress.PlayFormat)).RegisterTo(destroyCancellationToken);
            }
            else if (param.TryGetMedleyMatchProgress(out var medleyProgress))
            {
                var leftUnit = results[0].Unit;
                var rightUnit = results[1].Unit;
                if (medleyProgress.PlayFormat.RequestFormat.Legs.MaxLegsCount == 1 &&
                    medleyProgress.PlayFormat.RequestFormat.Sets.Value == 1)
                {
                    GameCode? gameCode = medleyProgress.PlayFormat.RequestFormat.GetGameCode(1, 1);
                    _title.text = $"{gameCode.Value.LongName()}";
                }
                else
                {
                    _title.text = GameFormatUtility.SetsLegsText(medleyProgress.PlayFormat.RequestFormat.Sets, medleyProgress.PlayFormat.RequestFormat.Legs);
                    //Setが2Set以上ならLegの表示は出さずSetの表示のみ出す
                    //Setが1Setの時にLegの表示を出してSetは出さない
                    var setCount = medleyProgress.PlayFormat.RequestFormat.Sets.Value;
                    if (setCount > 1)
                    {
                        _setLeft.Bind(medleyProgress.SetCountWonBy(leftUnit.Id));
                        _setRight.Bind(medleyProgress.SetCountWonBy(rightUnit.Id));
                    }
                    else
                    {
                        var (leftLegWonCount, rightLegWonCount) = Enumerable.Range(1, setCount).Aggregate(
                            (Left: 0, Right: 0),
                            (counts, i) => (
                                counts.Left + medleyProgress.LegCountWonBy(i, leftUnit.Id),
                                counts.Right + medleyProgress.LegCountWonBy(i, rightUnit.Id)
                            )
                        );

                        _legLeft.Bind(leftLegWonCount);
                        _legRight.Bind(rightLegWonCount);
                    }
                }

                ShowMedleyMergedResultData(param.GetGameResultsByUnit(leftUnit.Id).ToArray(), param.GetGameResultsByUnit(rightUnit.Id).ToArray(), medleyProgress.Winner, medleyProgress.PlayFormat.RequestFormat.OutCondition);
                _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(medleyProgress.PlayFormat)).RegisterTo(destroyCancellationToken);
            }
        }


        private void ShowResultData(GameResultByUnit leftUnit, GameResultByUnit rightUnit, UnitId winnerId, IResultItemFactory factory)
        {
            var leftUnitDataItems = factory.Create(leftUnit);
            var rightUnitDataItems = factory.Create(rightUnit);

            var datas = leftUnitDataItems.SelectMany((item, i) =>
            {
                var rightItem = rightUnitDataItems[i];
                return new[] {
                    (item.title, item.value, rightItem.value)
                };
            });

            foreach (var data in datas)
            {
                var Item = Instantiate(_GameResultDataItemPrefab, _GameResultData1v1Scroll.content);
                Item.Bind(data);
            }

            _leftUnitIcon.Bind(leftUnit.Unit, leftUnit.Unit.Id == winnerId);
            _rightUnitIcon.Bind(rightUnit.Unit, rightUnit.Unit.Id == winnerId);
        }

        private void ShowX01ResultData(GameResultByUnit leftUnit, GameResultByUnit rightUnit, UnitId winner, OutCondition outCondition){
            ShowResultData(leftUnit, rightUnit, winner, new ZeroOneResultItemFactory(outCondition));
        }

        private void ShowStandardCrResultData(GameResultByUnit leftUnit, GameResultByUnit rightUnit, UnitId winner){
            ShowResultData(leftUnit, rightUnit, winner, new CricketResultItemFactory());
        }
        private void ShowHiddenCrResultData(GameResultByUnit leftUnit, GameResultByUnit rightUnit, UnitId winner){
            ShowResultData(leftUnit, rightUnit, winner, new HiddenCricketResultItemFactory());
        }

        private void ShowCountUpResultData(GameResultByUnit leftUnit, GameResultByUnit rightUnit, UnitId winner)
        {
            ShowResultData(leftUnit, rightUnit, winner, new CountUpResultItemFactory());
        }

        private void ShowTarget20ResultData(GameResultByUnit leftUnit, GameResultByUnit rightUnit, UnitId winner)
        {
            ShowResultData(leftUnit, rightUnit, winner, new Target20ResultItemFactory());
        }
        private void ShowHalfItResultData(GameResultByUnit leftUnit, GameResultByUnit rightUnit, UnitId winner)
        {
            ShowResultData(leftUnit, rightUnit, winner, new HalfItResultItemFactory());
        }
        
        private void ShowRotationResultData(GameResultByUnit leftUnit, GameResultByUnit rightUnit, UnitId winner)
        {
            ShowResultData(leftUnit, rightUnit, winner, new RotationResultItemFactory());
        }

        private void ShowKickdownResultData(GameResultByUnit leftUnit, GameResultByUnit rightUnit, UnitId winner)
        {
            ShowResultData(leftUnit, rightUnit, winner, new KickdownResultItemFactory());
        }
        
        private void ShowBeyondTopResultData(GameResultByUnit leftUnit, GameResultByUnit rightUnit, UnitId winner)
        {
            ShowResultData(leftUnit, rightUnit, winner, new BeyondTopResultItemFactory());
        }
        
        private void ShowMultipleCRResultData(GameResultByUnit leftUnit, GameResultByUnit rightUnit, UnitId winner)
        {
            ShowResultData(leftUnit, rightUnit, winner, new MultipleCRResultItemFactory());
        }
        
        private void ShowShangHaiResultData(GameResultByUnit leftUnit, GameResultByUnit rightUnit, UnitId winner)
        {
            ShowResultData(leftUnit, rightUnit, winner, new ShangHaiResultItemFactory());
        }

        private void ShowMedleyMergedResultData(GameResultByUnit[] leftUnitLegsResult, GameResultByUnit[] rightUnitLegsResult, UnitId winnerId, OutCondition outCondition)
        {
            var leftMembersResult = leftUnitLegsResult.SelectMany(r => r.GameResultByMembers);
            var rightMembersResult = rightUnitLegsResult.SelectMany(r => r.GameResultByMembers);
            var leftMembersZeroOneResult = leftMembersResult.Where(r => r is ZeroOneGameResultByPlayer).Select(r => r as ZeroOneGameResultByPlayer).ToArray();
            var rightMembersZeroOneResult = rightMembersResult.Where(r => r is ZeroOneGameResultByPlayer).Select(r => r as ZeroOneGameResultByPlayer).ToArray();
            var leftMembersCricketResult = leftMembersResult.Where(r => r is StandardCrGameResultByPlayer).Select(r => r as StandardCrGameResultByPlayer).ToArray();
            var rightMembersCricketResult = rightMembersResult.Where(r => r is StandardCrGameResultByPlayer).Select(r => r as StandardCrGameResultByPlayer).ToArray();

            bool isMix = leftMembersZeroOneResult.Length > 0 && leftMembersCricketResult.Length > 0;
            bool isMedleyMatch = leftUnitLegsResult.Length > 1;
            // FinalScore is not used in this case. this data is temporary.
            var mergedLeftZeroOneResult = new GameResultByUnit(leftUnitLegsResult[0].Unit, leftMembersZeroOneResult, 0);
            var mergedRightZeroOneResult = new GameResultByUnit(rightUnitLegsResult[0].Unit, rightMembersZeroOneResult, 0);
            var mergedLeftCricketResult = new GameResultByUnit(leftUnitLegsResult[0].Unit, leftMembersCricketResult, leftUnitLegsResult.Sum(u => u.FinalScore));
            var mergedRightCricketResult = new GameResultByUnit(rightUnitLegsResult[0].Unit, rightMembersCricketResult, rightUnitLegsResult.Sum(u => u.FinalScore));

            var leftUnitZeroOneDataItems = new ZeroOneResultItemFactory(outCondition).Create(mergedLeftZeroOneResult);
            var leftUnitCricketDataItems = isMedleyMatch? new MedleyCricketResultItemFactory().Create(mergedLeftCricketResult) : new CricketResultItemFactory().Create(mergedLeftCricketResult);
            var rightUnitZeroOneDataItems = new ZeroOneResultItemFactory(outCondition).Create(mergedRightZeroOneResult);
            var rightUnitCricketDataItems = isMedleyMatch? new MedleyCricketResultItemFactory().Create(mergedRightCricketResult) : new CricketResultItemFactory().Create(mergedRightCricketResult);

            var zeroOneDatas = leftUnitZeroOneDataItems.SelectMany((item, i) => {
                var rightItem = rightUnitZeroOneDataItems[i];
                return new[] {
                    (item.title, item.value, rightItem.value)
                };
            });

            var cricketDatas = leftUnitCricketDataItems.SelectMany((item, i) => {
                var rightItem = rightUnitCricketDataItems[i];
                return new[] {
                    (item.title, item.value, rightItem.value)
                };
            });

            if(isMix) {
                var devider01 = Instantiate(_devider01, _GameResultData1v1Scroll.content);
                devider01.SetActive(true);
                foreach(var data in zeroOneDatas){
                    var Item = Instantiate(_GameResultDataItemPrefab,_GameResultData1v1Scroll.content);
                    Item.Bind(data);
                }
                var deviderCR = Instantiate(_deviderCR, _GameResultData1v1Scroll.content);
                deviderCR.SetActive(true);
                foreach(var data in cricketDatas){
                    var Item = Instantiate(_GameResultDataItemPrefab,_GameResultData1v1Scroll.content);
                    Item.Bind(data);
                }
            } else if(leftMembersZeroOneResult.Length > 0) {
                foreach(var data in zeroOneDatas){
                    var Item = Instantiate(_GameResultDataItemPrefab,_GameResultData1v1Scroll.content);
                    Item.Bind(data);
                }
            } else if(leftMembersCricketResult.Length > 0) {
                foreach(var data in cricketDatas){
                    var Item = Instantiate(_GameResultDataItemPrefab,_GameResultData1v1Scroll.content);
                    Item.Bind(data);
                }
            }

            _leftUnitIcon.Bind(leftUnitLegsResult[0].Unit, leftUnitLegsResult[0].Unit.Id == winnerId);
            _rightUnitIcon.Bind(rightUnitLegsResult[0].Unit, rightUnitLegsResult[0].Unit.Id == winnerId);
        }

        private async void OnClickGameEnd()
        {
            _btn_PlayAgain.loading = true;
            _btn_GameEnd.loading = true;
            try
            {
                await PageManager.Instance.BackSkip(PageNames.GameAndResultPages);
            }
            finally
            {
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
            }
        }

        private async void PlayAgain(x01PlayFormat playFormat) {
            _btn_PlayAgain.loading = true;
            _btn_GameEnd.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                DIContainer.Instance.Resolve<IMatchIDService>(),
                DIContainer.Instance.Resolve<IEventTracker>())
                .ExecuteAsync(playFormat, destroyCancellationToken);
                await PageManager.Instance.OpenAsync(
                        new PageMeta(
                            playFormat.EntrySlots.EnteredPlayers.Any(p => p.Settings.InputDevice == grandartslogic.InputDevice.GranEye) ?
                            PageNames.LocalX01_1vs1_GranEye :
                            PageNames.LocalX01_1vs1,
                            parameter: new Localx01PageParameter(
                                matchId,
                                playFormat
                            )
                        )
                    );
            } finally
            {
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
            }
        }

        private async void PlayAgain(CricketPlayFormat playFormat) {
            _btn_PlayAgain.loading = true;
            _btn_GameEnd.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                DIContainer.Instance.Resolve<IMatchIDService>(),
                DIContainer.Instance.Resolve<IEventTracker>())
                .ExecuteAsync(playFormat, destroyCancellationToken);
                if (playFormat.RequestFormat.GameCode == GameCode._StandardCR)
                {
                    await PageManager.Instance.OpenAsync(
                            new PageMeta(
                                playFormat.EntrySlots.EnteredPlayers.Any(p => p.Settings.InputDevice == grandartslogic.InputDevice.GranEye) ?
                                PageNames.LocalCR_1vs1_GranEye :
                                PageNames.LocalCR_1vs1,
                                parameter: new LocalStandardCRPageParameter(
                                    matchId,
                                    playFormat
                                )
                            )
                        );
                }else if(playFormat.RequestFormat.GameCode == GameCode._HiddenCR)
                {
                    await PageManager.Instance.OpenAsync(
                            new PageMeta(
                                playFormat.EntrySlots.EnteredPlayers.Any(p => p.Settings.InputDevice == grandartslogic.InputDevice.GranEye) ?
                                PageNames.LocalHiddenCR_1vs1_GranEye :
                                PageNames.LocalHiddenCR_1vs1,
                                parameter: new LocalStandardCRPageParameter(
                                    matchId,
                                    playFormat
                                )
                            )
                        );
                }
                else
                {
                    throw new ArgumentException("Invalid game code for cricket.");
                }
            }
            finally
            {
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
            }
        }

        private async void PlayAgain(CountUpPlayFormat playFormat) {
            _btn_PlayAgain.loading = true;
            _btn_GameEnd.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                DIContainer.Instance.Resolve<IMatchIDService>(),
                DIContainer.Instance.Resolve<IEventTracker>())
                .ExecuteAsync(playFormat, destroyCancellationToken);
                await PageManager.Instance.OpenAsync(
                        new PageMeta(
                            playFormat.EntrySlots.EnteredPlayers.Any(p => p.Settings.InputDevice == grandartslogic.InputDevice.GranEye) ?
                            PageNames.LocalCountUp_1vs1_GranEye :
                            PageNames.LocalCountUp_1vs1,
                            parameter: new LocalCountUpPageParameter(
                                matchId,
                                playFormat
                            )
                        )
                    );
            }
            finally
            {
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
            }
        }

        private async void PlayAgain(Target20PlayFormat playFormat) {
            _btn_PlayAgain.loading = true;
            _btn_GameEnd.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                        DIContainer.Instance.Resolve<IMatchIDService>(),
                        DIContainer.Instance.Resolve<IEventTracker>())
                    .ExecuteAsync(playFormat, destroyCancellationToken);
                await PageManager.Instance.OpenAsync(
                    new PageMeta(
                        playFormat.EntrySlots.EnteredPlayers.Any(p => p.Settings.InputDevice == grandartslogic.InputDevice.GranEye) ?
                            PageNames.LocalTarget20_1vs1_GranEye :
                            PageNames.LocalTarget20_1vs1,
                        parameter: new LocalTarget20PageParameter(
                            matchId,
                            playFormat
                        )
                    )
                );
            }
            finally
            {
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
            }
        }
        
        private async void PlayAgain(HalfItPlayFormat playFormat)
        {
            _btn_GameEnd.loading = true;
            _btn_PlayAgain.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                    DIContainer.Instance.Resolve<IMatchIDService>(),
                    DIContainer.Instance.Resolve<IEventTracker>())
                .ExecuteAsync(playFormat, destroyCancellationToken);
                await PageManager.Instance.OpenAsync(
                        new PageMeta(
                            playFormat.EntrySlots.EnteredPlayers.Any(p => p.Settings.InputDevice == grandartslogic.InputDevice.GranEye) ?
                            PageNames.LocalHalfIt_SinglesOrMulti_GranEye :
                            PageNames.LocalHalfIt_SinglesOrMulti,
                            parameter: new LocalHalfItPageParameter(
                                matchId,
                                playFormat
                            )
                        )
                    );
            }
            finally
            {
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
            }
        }
        
        private async void PlayAgain(RotationPlayFormat playFormat)
        {
            _btn_GameEnd.loading = true;
            _btn_PlayAgain.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                        DIContainer.Instance.Resolve<IMatchIDService>(),
                        DIContainer.Instance.Resolve<IEventTracker>())
                    .ExecuteAsync(playFormat, destroyCancellationToken);
                await PageManager.Instance.OpenAsync(
                    new PageMeta(
                        playFormat.EntrySlots.EnteredPlayers.Any(p => p.Settings.InputDevice == grandartslogic.InputDevice.GranEye) ?
                            PageNames.LocalRotation_SinglesOrMulti_GranEye :
                            PageNames.LocalRotation_SinglesOrMulti,
                        parameter: new LocalRotationPageParameter(
                            matchId,
                            playFormat
                        )
                    )
                );
            }
            finally
            {
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
            }
        }

        private async void PlayAgain(KickDownPlayFormat playFormat)
        {
            _btn_PlayAgain.loading = true;
            _btn_GameEnd.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                DIContainer.Instance.Resolve<IMatchIDService>(),
                DIContainer.Instance.Resolve<IEventTracker>()).ExecuteAsync(playFormat, destroyCancellationToken);
                await PageManager.Instance.OpenAsync(
                        new PageMeta(
                            playFormat.EntrySlots.EnteredPlayers.Any(p => p.Settings.InputDevice == grandartslogic.InputDevice.GranEye) ?
                            PageNames.LocalKickDown_1vs1_GranEye :
                            PageNames.LocalKickDown_1vs1,
                            parameter: new LocalKickDownPageParameter(
                                matchId,
                                playFormat
                            )
                        )
                    );

            }
            finally
            {
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
            }
        }
        
        private async void PlayAgain(BeyondTopPlayFormat playFormat)
        {
            _btn_PlayAgain.loading = true;
            _btn_GameEnd.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                    DIContainer.Instance.Resolve<IMatchIDService>(),
                    DIContainer.Instance.Resolve<IEventTracker>()).ExecuteAsync(playFormat, destroyCancellationToken);
                await PageManager.Instance.OpenAsync(
                    new PageMeta(
                        playFormat.EntrySlots.EnteredPlayers.Any(p => p.Settings.InputDevice == grandartslogic.InputDevice.GranEye) ?
                            PageNames.LocalBeyondTop_1vs1_GranEye :
                            PageNames.LocalBeyondTop_1vs1,
                        parameter: new LocalBeyondTopPageParameter(
                            matchId,
                            playFormat
                        )
                    )
                );

            }
            finally
            {
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
            }
        }
        
        private async void PlayAgain(MultipleCRPlayFormat playFormat)
        {
            _btn_PlayAgain.loading = true;
            _btn_GameEnd.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                    DIContainer.Instance.Resolve<IMatchIDService>(),
                    DIContainer.Instance.Resolve<IEventTracker>()).ExecuteAsync(playFormat, destroyCancellationToken);
                await PageManager.Instance.OpenAsync(
                    new PageMeta(
                        playFormat.EntrySlots.EnteredPlayers.Any(p => p.Settings.InputDevice == grandartslogic.InputDevice.GranEye) ?
                            PageNames.LocalMultipleCR_1vs1_GranEye :
                            PageNames.LocalMultipleCR_1vs1,
                        parameter: new LocalMultipleCRPageParameter(
                            matchId,
                            playFormat
                        )
                    )
                );

            }
            finally
            {
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
            }
        }
        
        private async void PlayAgain(ShangHaiPlayFormat playFormat)
        {
            _btn_PlayAgain.loading = true;
            _btn_GameEnd.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                    DIContainer.Instance.Resolve<IMatchIDService>(),
                    DIContainer.Instance.Resolve<IEventTracker>()).ExecuteAsync(playFormat, destroyCancellationToken);
                await PageManager.Instance.OpenAsync(
                    new PageMeta(
                        playFormat.EntrySlots.EnteredPlayers.Any(p => p.Settings.InputDevice == grandartslogic.InputDevice.GranEye) ?
                            PageNames.LocalShanghai_1vs1_GranEye :
                            PageNames.LocalShanghai_1vs1,
                        parameter: new LocalShangHaiPageParameter(
                            matchId,
                            playFormat
                        )
                    )
                );
            }
            finally
            {
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
            }
        }

        private async void PlayAgain(MedleyPlayFormat playFormat) {
            _btn_PlayAgain.loading = true;
            _btn_GameEnd.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                DIContainer.Instance.Resolve<IMatchIDService>(),
                DIContainer.Instance.Resolve<IEventTracker>())
            .ExecuteAsync(playFormat, destroyCancellationToken);

                if (playFormat.RequestFormat.GetGameCode(1, 1).Is01())
                {
                    await PageManager.Instance.OpenAsync(
                        new PageMeta(
                            playFormat.EntrySlots.EnteredPlayers.Any(p => p.Settings.InputDevice == grandartslogic.InputDevice.GranEye) ?
                            PageNames.LocalX01_1vs1_GranEye :
                            PageNames.LocalX01_1vs1,
                            parameter: new Localx01PageParameter(
                                matchId,
                                playFormat
                            )
                        )
                    );
                }
                else if (playFormat.RequestFormat.GetGameCode(1, 1) == GameCode._StandardCR)
                {
                    await PageManager.Instance.OpenAsync(
                            new PageMeta(
                                playFormat.EntrySlots.EnteredPlayers.Any(p => p.Settings.InputDevice == grandartslogic.InputDevice.GranEye) ?
                                PageNames.LocalCR_1vs1_GranEye :
                                PageNames.LocalCR_1vs1,
                                parameter: new LocalStandardCRPageParameter(
                                    matchId,
                                    playFormat
                                )
                            )
                        );
                }
                else
                {
                    throw new ArgumentException("Invalid game code for medley.");
                }
            }
            finally
            {
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
            }
        }
    }
}
