using R3;
using UnityEngine;
using com.luxza.ui.page;
using UnityEngine.UI;
using com.luxza.ui.components.molecules;
using com.luxza.grandarts.userinterfaces.page.parameters;
using com.luxza.grandarts.userinterfaces.components.result;
using com.luxza.grandarts.domains.game.unit;
using com.luxza.ui.components.atoms;
using System.Linq;
using com.luxza.grandarts.userinterfaces.page.utils;
using com.luxza.grandarts.domains.game.format;
using com.luxza.grandarts.usecases.game;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandarts.dicontainer;
using System;
using com.luxza.grandarts.domains.game.result;
using com.luxza.grandartslogic.domain.game.zeroone;
using com.luxza.grandarts.usecases.analysis;


namespace com.luxza.grandarts.userinterfaces.page.result
{
    public class PageGameResultMultiView : MonoBehaviour
    {
        [SerializeField] private Page _page;
        [SerializeField] private GranText _title;
        [SerializeField] private GranButton _btn_GameEnd;
        [SerializeField] private GranButton _btn_PlayAgain;
        [SerializeField] private GranText _GameResultDataTitlePrefab;
        [SerializeField] private GameResultUnitIcon _GameResultUnitIconsPrefab;
        [SerializeField] private GranText _finalScoreTextPrefab;
        [SerializeField] private GranText _valueTextPrefab;
        [SerializeField] private ScrollRect _valuesScrollRect;
        [SerializeField] private ScrollRect _titlesHeaderScrollRect;
        [SerializeField] private ScrollRect _iconsHeaderScrollRect;

        private void Start()
        {
            _page.OnActivate.Subscribe(OnActivate).RegisterTo(destroyCancellationToken);
        }

        private void OnActivate(Unit unit)
        {
            if(_page.Meta.TryGetParameter<GameResultParameter>(out var param)) {
                Init(param);
                _btn_GameEnd.onClickAsObservable.Subscribe(_ => OnClickGameEnd()).RegisterTo(destroyCancellationToken);
            } else {
                throw new ArgumentException("Invalid parameter.");
            }
        }

        private void Init(GameResultParameter param)
        {
            var units = param.GetLatestGameResultsByUnit().ToArray();
            if (param.TryGetx01MatchProgress(out var x01Progress))
            {
                _title.text = $"{x01Progress.PlayFormat.RequestFormat.GameCode.LongName()}";
                var results = param.GetLatestGameResultsByUnit().ToArray();
                if (results.Length <= 2)
                {
                    throw new ArgumentException("Invalid game result count.");
                }

                ShowX01ResultData(results, param.GetLatestResult().Winner, x01Progress.PlayFormat.RequestFormat.OutCondition);
                _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(x01Progress.PlayFormat)).RegisterTo(destroyCancellationToken);
            }
            else if (param.TryGetStandardCRMatchProgress(out var cRProgress))
            {
                _title.text = $"{cRProgress.PlayFormat.RequestFormat.GameCode.LongName()}";
                ShowStandardCrResultData(units, param.GetLatestResult().Winner);
                _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(cRProgress.PlayFormat)).RegisterTo(destroyCancellationToken);
            }
            else if (param.TryGetCutthroatCRProgress(out var cutthroatProgress))
            {
                _title.text = $"{cutthroatProgress.PlayFormat.RequestFormat.GameCode.LongName()}";
                if (cutthroatProgress.PlayFormat.RequestFormat.GameCode == GameCode._HiddenCutThroat)
                {
                    ShowHiddenCutthroatCrResultData(units, param.GetLatestResult().Winner);
                }
                else
                {
                    ShowCutthroatCrResultData(units, param.GetLatestResult().Winner);
                }

                _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(cutthroatProgress.PlayFormat)).RegisterTo(destroyCancellationToken);
            }
            else if (param.TryGetCountUpProgress(out var countUpProgress))
            {
                _title.text = $"{countUpProgress.PlayFormat.RequestFormat.GameCode.LongName()}";
                ShowCountUpResultData(units, countUpProgress.Winner);
                _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(countUpProgress.PlayFormat)).RegisterTo(destroyCancellationToken);
            }
            else if (param.TryGetTarget20Progress(out var target20Progress))
            {
                _title.text = $"{target20Progress.PlayFormat.RequestFormat.GameCode.LongName()}";
                ShowTarget20ResultData(units, target20Progress.Winner);
                _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(target20Progress.PlayFormat)).RegisterTo(destroyCancellationToken);
            }
            else if (param.TryGetHalfItProgress(out var halfItProgress))
            {
                _title.text = $"{halfItProgress.PlayFormat.RequestFormat.GameCode.LongName()}";
                ShowCountUpResultData(units, halfItProgress.Winner);
                _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(halfItProgress.PlayFormat)).RegisterTo(destroyCancellationToken);
            }
            else if (param.TryGetRotationProgress(out var rotationProgress))
            {
                _title.text = $"{rotationProgress.PlayFormat.RequestFormat.GameCode.LongName()}";
                ShowRotationResultData(units, rotationProgress.Winner);
                _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(rotationProgress.PlayFormat)).RegisterTo(destroyCancellationToken);
            }
            else if (param.TryGetKickdownProgress(out var kickdownProgress))
            {
                _title.text = $"{kickdownProgress.PlayFormat.RequestFormat.GameCode.LongName()}";
                ShowKickdownResultData(units, kickdownProgress.Winner);
                _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(kickdownProgress.PlayFormat)).RegisterTo(destroyCancellationToken);
            }
            else if (param.TryGetBeyondTopProgress(out var beyondTopProgress))
            {
                _title.text = $"{beyondTopProgress.PlayFormat.RequestFormat.GameCode.LongName()}";
                ShowBeyondTopResultData(units, beyondTopProgress.Winner);
                _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(beyondTopProgress.PlayFormat)).RegisterTo(destroyCancellationToken);
            }
            else if (param.TryGetMultipleCRProgress(out var multipleCRProgress))
            {
                _title.text = $"{multipleCRProgress.PlayFormat.RequestFormat.GameCode.LongName()}";
                ShowMultipleCRResultData(units, multipleCRProgress.Winner);
                _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(multipleCRProgress.PlayFormat)).RegisterTo(destroyCancellationToken);
            }
            else if (param.TryGetShangHaiProgress(out var shangHaiProgress))
            {
                _title.text = $"{shangHaiProgress.PlayFormat.RequestFormat.GameCode.LongName()}";
                ShowShangHaiResultData(units, shangHaiProgress.Winner);
                _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(shangHaiProgress.PlayFormat)).RegisterTo(destroyCancellationToken);
            }
            else
            {
                throw new ArgumentException("Invalid parameter.");
            }
        }

        private void ShowResultData(GameResultByUnit[] units, UnitId winnerUnitId, IResultItemFactory factory) {
            int i = 0;
            foreach(var unit in units) {
                var data = factory.Create(unit);
                if (i == 0)
                {
                    var grid = _valuesScrollRect.content.GetComponent<GridLayoutGroup>();
                    grid.constraintCount = data.Count;
                    grid.constraint = GridLayoutGroup.Constraint.FixedRowCount;

                    foreach (var (title, unitData) in data) {
                        var titleText = Instantiate(_GameResultDataTitlePrefab, _titlesHeaderScrollRect.content);
                        titleText.text = title;
                        titleText.gameObject.SetActive(true);
                    }
                }

                var iconItem = Instantiate(_GameResultUnitIconsPrefab, _iconsHeaderScrollRect.content);
                iconItem.Bind(unit.Unit, unit.Unit.Id == winnerUnitId);
                iconItem.gameObject.SetActive(true);

                foreach (var (title, unitData) in data)
                {
                    var valueText = Instantiate(_valueTextPrefab, _valuesScrollRect.content);
                    valueText.text = unitData;
                    valueText.gameObject.SetActive(true);
                }

                i++;
            }
        }

        private void ShowX01ResultData(GameResultByUnit[] units, UnitId winnerUnitId, OutCondition outCondition)
        {
            ShowResultData(units, winnerUnitId, new ZeroOneResultItemFactory(outCondition));
        }

        private void ShowStandardCrResultData(GameResultByUnit[] units, UnitId winner) {
            ShowResultData(units, winner, new CricketResultItemFactory());
        }
        
        private void ShowCutthroatCrResultData(GameResultByUnit[] units, UnitId winner) {
            ShowResultData(units, winner, new CutthroatCRResultItemFactory());
        }

        private void ShowHiddenCutthroatCrResultData(GameResultByUnit[] units, UnitId winner) {
            ShowResultData(units, winner, new HiddenCutthroatCRResultItemFactory());
        }

        private void ShowCountUpResultData(GameResultByUnit[] units, UnitId winner)
        {
            ShowResultData(units, winner, new CountUpResultItemFactory());
        }
        
        private void ShowTarget20ResultData(GameResultByUnit[] units,UnitId winner) {
            ShowResultData(units, winner, new Target20ResultItemFactory());
        }

        private void ShowRotationResultData(GameResultByUnit[] units,UnitId winner) {
            ShowResultData(units, winner, new RotationResultItemFactory());
        }
        
        private void ShowKickdownResultData(GameResultByUnit[] units,UnitId winner) {
            ShowResultData(units, winner, new KickdownResultItemFactory());
        }
        
        private void ShowBeyondTopResultData(GameResultByUnit[] units,UnitId winner) {
            ShowResultData(units, winner, new BeyondTopResultItemFactory());
        }
        
        private void ShowMultipleCRResultData(GameResultByUnit[] units,UnitId winner) {
            ShowResultData(units, winner, new MultipleCRResultItemFactory());
        }
        
        private void ShowShangHaiResultData(GameResultByUnit[] units,UnitId winner) {
            ShowResultData(units, winner, new ShangHaiResultItemFactory());
        }

        private async void OnClickGameEnd()
        {
            _btn_GameEnd.loading = true;
            _btn_PlayAgain.loading = true;
            try
            {
                await PageManager.Instance.BackSkip(PageNames.GameAndResultPages);
            } finally
            {
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
            }
        }

        private async void PlayAgain(x01PlayFormat playFormat) {
            _btn_GameEnd.loading = true;
            _btn_PlayAgain.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                    DIContainer.Instance.Resolve<IMatchIDService>(),
                    DIContainer.Instance.Resolve<IEventTracker>())
                .ExecuteAsync(playFormat, destroyCancellationToken);
                await PageManager.Instance.OpenAsync(
                        new PageMeta(
                            playFormat.EntrySlots.EnteredPlayers.Any(p => p.Settings.InputDevice == grandartslogic.InputDevice.GranEye) ?
                            PageNames.LocalX01_SinglesOrMulti_GranEye :
                            PageNames.LocalX01_SinglesOrMulti,
                            parameter: new Localx01PageParameter(
                                matchId,
                                playFormat
                            )
                        )
                    );
            }
            finally
            {
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
            }
        }

        private async void PlayAgain(CricketPlayFormat playFormat) {
            _btn_GameEnd.loading = true;
            _btn_PlayAgain.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                    DIContainer.Instance.Resolve<IMatchIDService>(),
                    DIContainer.Instance.Resolve<IEventTracker>())
                .ExecuteAsync(playFormat, destroyCancellationToken);
                await PageManager.Instance.OpenAsync(
                        new PageMeta(
                            playFormat.EntrySlots.EnteredPlayers.Any(p => p.Settings.InputDevice == grandartslogic.InputDevice.GranEye) ?
                            PageNames.LocalCR_SinglesOrMulti_GranEye :
                            PageNames.LocalCR_SinglesOrMulti,
                            parameter: new LocalStandardCRPageParameter(
                                matchId,
                                playFormat
                            )
                        )
                    );
            }
            finally
            {
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
            }
        }
        
        private async void PlayAgain(CutthroatPlayFormat playFormat) {
            _btn_GameEnd.loading = true;
            _btn_PlayAgain.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                    DIContainer.Instance.Resolve<IMatchIDService>(),
                    DIContainer.Instance.Resolve<IEventTracker>())
                .ExecuteAsync(playFormat, destroyCancellationToken);
                await PageManager.Instance.OpenAsync(
                        new PageMeta(
                            playFormat.EntrySlots.EnteredPlayers.Any(p => p.Settings.InputDevice == grandartslogic.InputDevice.GranEye) ?
                            PageNames.LocalCutthroat_Multi_GranEye :
                            PageNames.LocalCutthroat_Multi,
                            parameter: new LocalStandardCRPageParameter(
                                matchId,
                                playFormat
                            )
                        )
                    );
            }
            finally
            {
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
            }
        }

        private async void PlayAgain(CountUpPlayFormat playFormat)
        {
            _btn_GameEnd.loading = true;
            _btn_PlayAgain.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                    DIContainer.Instance.Resolve<IMatchIDService>(),
                    DIContainer.Instance.Resolve<IEventTracker>())
                .ExecuteAsync(playFormat, destroyCancellationToken);
                await PageManager.Instance.OpenAsync(
                        new PageMeta(
                            playFormat.EntrySlots.EnteredPlayers.Any(p => p.Settings.InputDevice == grandartslogic.InputDevice.GranEye) ?
                            PageNames.LocalCountUp_SinglesOrMulti_GranEye :
                            PageNames.LocalCountUp_SinglesOrMulti,
                            parameter: new LocalCountUpPageParameter(
                                matchId,
                                playFormat
                            )
                        )
                    );
            }
            finally
            {
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
            }
        }

        private async void PlayAgain(Target20PlayFormat playFormat)
        {
            _btn_GameEnd.loading = true;
            _btn_PlayAgain.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                        DIContainer.Instance.Resolve<IMatchIDService>(),
                        DIContainer.Instance.Resolve<IEventTracker>())
                    .ExecuteAsync(playFormat, destroyCancellationToken);
                await PageManager.Instance.OpenAsync(
                    new PageMeta(
                        playFormat.EntrySlots.EnteredPlayers.Any(p => p.Settings.InputDevice == grandartslogic.InputDevice.GranEye) ?
                            PageNames.LocalTarget20_SinglesOrMulti_GranEye :
                            PageNames.LocalTarget20_SinglesOrMulti,
                        parameter: new LocalTarget20PageParameter(
                            matchId,
                            playFormat
                        )
                    )
                );
            }
            finally
            {
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
            }
        }
        private async void PlayAgain(HalfItPlayFormat playFormat) {
            _btn_GameEnd.loading = true;
            _btn_PlayAgain.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                    DIContainer.Instance.Resolve<IMatchIDService>(),
                    DIContainer.Instance.Resolve<IEventTracker>())
                .ExecuteAsync(playFormat, destroyCancellationToken);
                await PageManager.Instance.OpenAsync(
                        new PageMeta(
                            playFormat.EntrySlots.EnteredPlayers.Any(p => p.Settings.InputDevice == grandartslogic.InputDevice.GranEye) ?
                            PageNames.LocalHalfIt_SinglesOrMulti_GranEye :
                            PageNames.LocalHalfIt_SinglesOrMulti,
                            parameter: new LocalHalfItPageParameter(
                                matchId,
                                playFormat
                            )
                        )
                    );
            }
            finally
            {
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
            }
        }
        
        private async void PlayAgain(RotationPlayFormat playFormat) {
            _btn_GameEnd.loading = true;
            _btn_PlayAgain.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                        DIContainer.Instance.Resolve<IMatchIDService>(),
                        DIContainer.Instance.Resolve<IEventTracker>())
                    .ExecuteAsync(playFormat, destroyCancellationToken);
                await PageManager.Instance.OpenAsync(
                    new PageMeta(
                        playFormat.EntrySlots.EnteredPlayers.Any(p => p.Settings.InputDevice == grandartslogic.InputDevice.GranEye) ?
                            PageNames.LocalRotation_SinglesOrMulti_GranEye :
                            PageNames.LocalRotation_SinglesOrMulti,
                        parameter: new LocalRotationPageParameter(
                            matchId,
                            playFormat
                        )
                    )
                );
            }
            finally
            {
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
            }
        }

        private async void PlayAgain(KickDownPlayFormat playFormat)
        {
            _btn_GameEnd.loading = true;
            _btn_PlayAgain.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                    DIContainer.Instance.Resolve<IMatchIDService>(),
                    DIContainer.Instance.Resolve<IEventTracker>())
                .ExecuteAsync(playFormat, destroyCancellationToken);
                await PageManager.Instance.OpenAsync(
                        new PageMeta(
                            playFormat.EntrySlots.EnteredPlayers.Any(p => p.Settings.InputDevice == grandartslogic.InputDevice.GranEye) ?
                            PageNames.LocalKickDown_SinglesOrMulti_GranEye :
                            PageNames.LocalKickDown_SinglesOrMulti,
                            parameter: new LocalKickDownPageParameter(
                                matchId,
                                playFormat
                            )
                        )
                    );
            }
            finally
            {
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
            }
        }
        
        private async void PlayAgain(BeyondTopPlayFormat playFormat)
        {
            _btn_GameEnd.loading = true;
            _btn_PlayAgain.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                        DIContainer.Instance.Resolve<IMatchIDService>(),
                        DIContainer.Instance.Resolve<IEventTracker>())
                    .ExecuteAsync(playFormat, destroyCancellationToken);
                await PageManager.Instance.OpenAsync(
                    new PageMeta(
                        playFormat.EntrySlots.EnteredPlayers.Any(p => p.Settings.InputDevice == grandartslogic.InputDevice.GranEye) ?
                            PageNames.LocalBeyondTop_SinglesOrMulti_GranEye :
                            PageNames.LocalBeyondTop_SinglesOrMulti,
                        parameter: new LocalBeyondTopPageParameter(
                            matchId,
                            playFormat
                        )
                    )
                );
            }
            finally
            {
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
            }
        }
        
        private async void PlayAgain(MultipleCRPlayFormat playFormat)
        {
            _btn_GameEnd.loading = true;
            _btn_PlayAgain.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                        DIContainer.Instance.Resolve<IMatchIDService>(),
                        DIContainer.Instance.Resolve<IEventTracker>())
                    .ExecuteAsync(playFormat, destroyCancellationToken);
                await PageManager.Instance.OpenAsync(
                    new PageMeta(
                        playFormat.EntrySlots.EnteredPlayers.Any(p => p.Settings.InputDevice == grandartslogic.InputDevice.GranEye) ?
                            PageNames.LocalMultipleCR_SinglesOrMulti_GranEye :
                            PageNames.LocalMultipleCR_SinglesOrMulti,
                        parameter: new LocalMultipleCRPageParameter(
                            matchId,
                            playFormat
                        )
                    )
                );
            }
            finally
            {
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
            }
        }
        
        private async void PlayAgain(ShangHaiPlayFormat playFormat)
        {
            _btn_GameEnd.loading = true;
            _btn_PlayAgain.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                        DIContainer.Instance.Resolve<IMatchIDService>(),
                        DIContainer.Instance.Resolve<IEventTracker>())
                    .ExecuteAsync(playFormat, destroyCancellationToken);
                await PageManager.Instance.OpenAsync(
                    new PageMeta(
                        playFormat.EntrySlots.EnteredPlayers.Any(p => p.Settings.InputDevice == grandartslogic.InputDevice.GranEye) ?
                            PageNames.LocalShanghai_SinglesOrMulti_GranEye :
                            PageNames.LocalShanghai_SinglesOrMulti,
                        parameter: new LocalShangHaiPageParameter(
                            matchId,
                            playFormat
                        )
                    )
                );
            }
            finally
            {
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
            }
        }
    }
}
