using System;
using System.Collections.Generic;
using System.Linq;
using com.luxza.grandarts.domains.game.result;
using com.luxza.grandarts.domains.game.result.multiplecr;
using com.luxza.grandarts.domains.game.result.shanghai;

namespace com.luxza.grandarts.userinterfaces.page.result
{
    public class ShangHaiResultItemFactory : IResultItemFactory
    {
        public List<(string title, string value)> Create(GameResultByUnit unit)
        {
            var memberAnalysis = unit.GameResultByMembers.Select(m => {
                if (m is ShangHaiGameResultByPlayer player) return player.analysisData;
                throw new ArgumentException("Invalid game result type.");
            });
            // var unitAnalysis = ShangHaiAnalysisAggregator.Aggregate(memberAnalysis);
            var result = new List<(string title, string value)>
            {
                ("FINAL SCORE", unit.FinalScore.ToString()),
            };
            return result;
        }
    }
}