using System.Collections.Generic;
using UnityEngine;

namespace com.luxza.graneye
{
    /// <summary>
    /// Progress information for auto calibration
    /// </summary>
    public class AutoCalibrationProgress
    {
        /// <summary>
        /// Current processing step
        /// </summary>
        public string CurrentStep { get; set; }

        /// <summary>
        /// Progress percentage (0-100)
        /// </summary>
        public int ProgressPercentage { get; set; }

        /// <summary>
        /// Camera currently being processed
        /// </summary>
        public GranEyeCore.CameraPos? CurrentCamera { get; set; }

        /// <summary>
        /// Index of the point currently being processed
        /// </summary>
        public int? CurrentPointIndex { get; set; }
    }

    /// <summary>
    /// 自動キャリブレーション実行結果
    /// </summary>
    public class AutoCalibrationResult
    {
        /// <summary>
        /// 実行成功フラグ
        /// </summary>
        public bool IsSuccess { get; set; }
        
        /// <summary>
        /// エラーメッセージ（失敗時のみ）
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// 左カメラの検出点（Tag番号をキーとした座標辞書）
        /// </summary>
        public Dictionary<int, Vector3> LeftCameraPoints { get; set; }
        
        /// <summary>
        /// 右カメラの検出点（Tag番号をキーとした座標辞書）
        /// </summary>
        public Dictionary<int, Vector3> RightCameraPoints { get; set; }
        
        /// <summary>
        /// 左カメラの取得画像
        /// </summary>
        public GranEyeCore.CameraRespondData LeftCameraImage { get; set; }
        
        /// <summary>
        /// 右カメラの取得画像
        /// </summary>
        public GranEyeCore.CameraRespondData RightCameraImage { get; set; }

        /// <summary>
        /// 処理されたカメラ数
        /// </summary>
        public int ProcessedCameraCount { get; set; }

        /// <summary>
        /// 各カメラの検出された点数
        /// </summary>
        public Dictionary<GranEyeCore.CameraPos, int> DetectedPointsCount { get; set; }

        public AutoCalibrationResult()
        {
            LeftCameraPoints = new Dictionary<int, Vector3>();
            RightCameraPoints = new Dictionary<int, Vector3>();
            DetectedPointsCount = new Dictionary<GranEyeCore.CameraPos, int>();
        }
    }

    /// <summary>
    /// 単一カメラのキャリブレーション結果
    /// </summary>
    public class CameraCalibrationResult
    {
        /// <summary>
        /// 対象カメラ
        /// </summary>
        public GranEyeCore.CameraPos Camera { get; set; }

        /// <summary>
        /// 検出された点（Tag番号をキーとした座標辞書）
        /// </summary>
        public Dictionary<int, Vector3> DetectedPoints { get; set; }

        /// <summary>
        /// 相機圖片
        /// </summary>
        public Texture Image { get; set; }

        /// <summary>
        /// 実行成功フラグ
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// エラーメッセージ（失敗時のみ）
        /// </summary>
        public string ErrorMessage { get; set; }

        public CameraCalibrationResult()
        {
            DetectedPoints = new Dictionary<int, Vector3>();
        }
    }
}