using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace com.luxza.graneye
{

    /// <summary>
    /// GranEye Calibration Extensions
    /// </summary>
    /// <remarks>
    /// This class provides extension methods for GranEyeCore.
    /// A GranEyeCore instance is required before use.
    /// </remarks>
    public static class GranEyeCalibrationExtensions
    {
        /// <summary>
        /// Perform auto calibration (single camera) - Extension method
        /// </summary>
        /// <param name="granEyeCore">GranEyeCore instance</param>
        /// <param name="cameraPos">Target camera position</param>
        /// <param name="texture">Camera texture</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <param name="progress">Progress notification callback</param>
        /// <returns>Calibration result</returns>
        public static async UniTask<CameraCalibrationResult> PerformAutoCalibrationAsync(
            this GranEyeCore granEyeCore,
            GranEyeCore.CameraPos cameraPos, 
            Texture texture,
            CancellationToken cancellationToken = default,
            IProgress<AutoCalibrationProgress> progress = null)
        {
            var result = new CameraCalibrationResult
            {
                Camera = cameraPos,
                Image = texture,
                DetectedPoints = new Dictionary<int, Vector3>()
            };

            // Input texture validation
            if (texture == null)
            {
                result.IsSuccess = false;
                result.ErrorMessage = "Input texture is null.";
                Debug.LogError($"Auto calibration error: {result.ErrorMessage}");
                return result;
            }

            try
            {
                // Progress notification: Initialization
                progress?.Report(new AutoCalibrationProgress
                {
                    CurrentStep = "Initializing",
                    ProgressPercentage = 0,
                    CurrentCamera = cameraPos
                });

                // Initialize and verify Zoom model
                await granEyeCore.InitializeDartZoomPredictor();

                var culture = CultureInfo.InvariantCulture;
                var calibrationTool = granEyeCore.GetCalibrationTool(cameraPos);

                // Dictionary to store updated calibration points
                var calibrationPoints = new Dictionary<int, Vector3>();

                // Process each of the 4 calibration points
                for (var pointIndex = 0; pointIndex < 4; pointIndex++)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    // Progress notification: Start processing each point
                    progress?.Report(new AutoCalibrationProgress
                    {
                        CurrentStep = $"Processing calibration point {pointIndex + 1}/4",
                        ProgressPercentage = (pointIndex * 100) / 4,
                        CurrentCamera = cameraPos,
                        CurrentPointIndex = pointIndex
                    });

                    // Get original point position
                    var originalPos = granEyeCore.Get_Dot(cameraPos, CalibrationType.Auto, pointIndex);

                    // Get zoomed texture for this point
                    var virtualPoint = calibrationTool.GetVirtualPointFromReal(originalPos);
                    var zoomResult = granEyeCore.GetZoomedTexture((int)cameraPos, texture, virtualPoint);

                    // Null check for zoomResult.texture
                    if (zoomResult.texture == null)
                    {
                        Debug.LogError($"ZoomResult.texture for point {pointIndex} is null. Skipping this point.");
                        continue;
                    }

                    // This simulates the state of UI control points after handling overflow
                    var controlPointPosition = new Vector2(zoomResult.overflowX, zoomResult.overflowY);

                    // Use AI to predict dart position
                    var predictResults = await granEyeCore.PredictDartZoom(zoomResult.texture);
                    if (predictResults == null || predictResults.Count == 0)
                    {
                        Debug.LogWarning($"Could not obtain AI prediction result for point {pointIndex}. Texture size: {zoomResult.texture.width}x{zoomResult.texture.height}");
                        continue;
                    }

                    // Get best prediction
                    var bestPrediction = predictResults.OrderByDescending(p => p.score).First();

                    // Convert prediction to Unity coordinates
                    var unityPos = TflitePositionHelper.ModelPos2UnityPos(new Vector2(bestPrediction.x, bestPrediction.y));

                    // Calculate position in pixel coordinates based on zoomed image
                    var rectSize = new Vector2(zoomResult.texture.width, zoomResult.texture.height);
                    var pixPos = new Vector2(
                        unityPos.x * rectSize.x,
                        unityPos.y * rectSize.y
                    );

                    // Calculate offset (similar to _aiPredictedOffset - _UIControlPoint.anchoredPosition)
                    var deltaOffset = pixPos - controlPointPosition;

                    // Convert offset to normalized coordinates
                    var sourceWidth = texture.width;
                    var normalizedOffsetX = deltaOffset.x / sourceWidth;
                    var normalizedOffsetY = deltaOffset.y / sourceWidth;

                    // Calculate final position
                    var finalPos = new Vector2(
                        originalPos.x + normalizedOffsetX,
                        originalPos.y + normalizedOffsetY
                    );

                    // Store updated position
                    calibrationPoints[pointIndex] = new Vector3(finalPos.x, finalPos.y, 0);
                    granEyeCore.Set_Dot(cameraPos, CalibrationType.Auto, pointIndex, finalPos);
                }

                // Perform calibration using updated points
                if (calibrationPoints.Count == 4)
                {
                    granEyeCore.Calibration(cameraPos, calibrationPoints.Values.ToList());
                    Debug.Log($"Auto calibration completed: {cameraPos} - All 4 points successful");
                    
                    result.IsSuccess = true;
                    result.DetectedPoints = calibrationPoints;

                    // Progress notification: Completed
                    progress?.Report(new AutoCalibrationProgress
                    {
                        CurrentStep = "Calibration completed",
                        ProgressPercentage = 100,
                        CurrentCamera = cameraPos
                    });
                }
                else
                {
                    Debug.LogWarning($"Auto calibration completed: {cameraPos} - Only {calibrationPoints.Count}/4 points successful");
                    result.IsSuccess = false;
                    result.ErrorMessage = $"Only {calibrationPoints.Count} out of 4 points were detected.";
                    result.DetectedPoints = calibrationPoints;
                }
            }
            catch (OperationCanceledException)
            {
                result.IsSuccess = false;
                result.ErrorMessage = "Calibration was cancelled.";
                throw;
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.ErrorMessage = $"Error occurred during auto calibration: {ex.Message}";
                Debug.LogError($"Auto calibration error: {cameraPos} - {ex.Message}");
            }

            return result;
        }

    }
}