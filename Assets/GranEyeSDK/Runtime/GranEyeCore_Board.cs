using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using TensorFlowLite;
using UnityEngine;
using com.luxza.granlog;

namespace com.luxza.graneye
{
    public partial class GranEyeCore
    {
        #region 镖靶模型相关
        private Board4PointPredictor m_Board4PointPredictor;

        private CalibrationTool[] m_CalibrationTools;

        public CalibrationTool GetCalibrationTool(CameraPos pos)
        {
            return m_CalibrationTools[(int)pos];
        }

        /// <summary>
        /// Explicitly initialize the board detector when needed
        /// </summary>
        /// <returns>A task representing the initialization process</returns>
        public async Task InitializeBoardDetector()
        {
            if (m_Board4PointPredictor != null)
            {
                Log.d("Board detector already initialized.");
                return;
            }

            await InitBoardDetector();
            Log.d("Board detector initialized successfully.");
        }

        /// <summary>
        /// Terminate the board detector when no longer needed
        /// </summary>
        public void TerminateBoardDetector()
        {
            if (m_Board4PointPredictor == null)
            {
                Log.d("Board detector is not initialized.");
                return;
            }

            // 先调用Dispose方法释放TensorFlow资源
            try
            {
                // 尝试调用IDeepLearnDetector接口的Dispose方法来释放TensorFlow资源
                // 这确保了底层的TensorFlow解释器和相关资源被正确释放
                var detector = m_Board4PointPredictor.GetComponent<IDeepLearnDetector>();
                if (detector != null)
                {
                    detector.Dispose();
                    Log.d("Board detector resources disposed successfully.");
                }
            }
            catch (Exception ex)
            {
                Log.e($"Error disposing board detector resources: {ex.Message}");
            }

            // 然后销毁 Board4PointPredictorByOD 游戏对象
            if (m_Board4PointPredictor.gameObject != null)
            {
                GameObject.Destroy(m_Board4PointPredictor.gameObject);

                // 检查容器是否为空，如果为空则销毁容器
                GameObject containerObj = GameObject.Find("GranEyeSDK_DontDestroy");
                if (containerObj != null && containerObj.transform.childCount == 0)
                {
                    GameObject.Destroy(containerObj);
                }
            }

            // 清空引用
            m_Board4PointPredictor = null;

            Log.d("Board detector terminated successfully.");
        }

        public async Task UpdateBoardDetector(float conf, float iou)
        {
            await m_Board4PointPredictor.Init("Calibration_pose.tflite",m_Accelerator);
        }

        private async Task UpdateBoardDetectorAccelerator(TfLiteDelegateType accelerator)
        {
            var t_Conf = Get_Board4Point_Conf();
            var t_Iou = Get_Board4Point_IoU();
            await m_Board4PointPredictor.Init("Calibration_pose.tflite", accelerator);
        }

        private async Task InitBoardDetector()
        {
            var t_Conf = Get_Board4Point_Conf();
            var t_Iou = Get_Board4Point_IoU();

            // 先查找已存在的DontDestroy容器
            var containerObj = GameObject.Find("GranEyeSDK_DontDestroy");

            if (containerObj == null)
            {
                // 创建容器并设置为DontDestroyOnLoad
                containerObj = new GameObject("GranEyeSDK_DontDestroy");
                UnityEngine.Object.DontDestroyOnLoad(containerObj);
            }

            // 查找已有的检测器（在DontDestroyOnLoad容器下查找）
            var existingDetector = containerObj.GetComponentInChildren<Board4PointPredictor>(true);

            if (existingDetector != null)
            {
                // 使用已存在的检测器
                m_Board4PointPredictor = existingDetector;
            }
            else
            {
                // 销毁场景中可能存在的其他检测器实例
                Board4PointPredictor[] otherDetectors = UnityEngine.Object.FindObjectsByType<Board4PointPredictor>(FindObjectsSortMode.None);
                foreach (var detector in otherDetectors)
                {
                    if (detector.transform.parent == null || detector.transform.parent.name != "GranEyeSDK_DontDestroy")
                    {
                        GameObject.Destroy(detector.gameObject);
                    }
                }

                // 创建新的检测器作为容器的子对象
                var detectorObj = new GameObject("Board4PointPredictor");
                detectorObj.transform.SetParent(containerObj.transform);

                m_Board4PointPredictor = detectorObj.AddComponent<Board4PointPredictor>();
            }

            // 初始化检测器
            await m_Board4PointPredictor.Init("Calibration_pose.tflite", TfLiteDelegateType.XNNPACK);
        }

        public async Task<List<TFEntities.PredictResultPointEntity>> PredictBoard(Texture texture)
        {
            var tPoints = await m_Board4PointPredictor.Predict(texture,
                CancellationToken.None);
            return tPoints;
        }

        public async Task CalibrateBothCameras()
        {
            this.m_CalibrationTools = new CalibrationTool[] { new CalibrationTool(), new CalibrationTool() };

            var leftTask = Task.FromResult(Calibration(CameraPos.Left));
            var rightTask = Task.FromResult(Calibration(CameraPos.Right));

            await Task.WhenAll(leftTask, rightTask);
        }

        public (bool,List<Vector3>) Calibration(CameraPos pos)
        {
            CultureInfo culture = CultureInfo.InvariantCulture;
            var isOk = false;
            var posList = new List<Vector3>();

            // Log.d($"{cameraIndex},Start Calibration");
            for (int i = 0; i < 4; i++)
            {
                var dot = Get_Dot(pos,CalibrationType.Auto,i);
                if (dot != Vector2.zero || dot != null)
                {
                    Log.d($"{dot.x}, {dot.y}");
                    var dotPos = new Vector3(dot.x, dot.y, 0);
                    posList.Add(dotPos);
                }
            }

            isOk = this.Calibration(pos, posList);

            return (isOk,posList);
        }
        public bool Calibration(CameraPos pos, List<Vector3> posList)
        {
            var isOk = false;
            if (posList.Count == 4)
            {
                Log.d($"{pos}, Calibration");
                var boardType = Get_BoardBrand();

                this.GetCalibrationTool(pos).Init(
                    posList.ToArray(),
                    (VirtualBoard.CameraPos)pos,
                    boardType);

                isOk = true;
            }
            return isOk;
        }

        #region 自動キャリブレーション統合機能

        /// <summary>
        /// 両カメラの自動キャリブレーションを実行
        /// </summary>
        /// <param name="includeZoomAdjustment">AI精密ズーム調整を含めるか</param>
        /// <param name="cancellationToken">キャンセレーション用トークン</param>
        /// <returns>自動キャリブレーション結果</returns>
        public async Task<AutoCalibrationResult> ExecuteAutoCalibrationAsync(
            bool includeZoomAdjustment = true,
            CancellationToken cancellationToken = default)
        {
            var result = new AutoCalibrationResult();

            try
            {
                 // 画像の取得
                if (Get_USBMode())
                {
                    // USB連接状態の検証
                    if (!IsUSBCamera1Connected || !IsUSBCamera2Connected)
                    {
                        result.IsSuccess = false;
                        result.ErrorMessage = $"USB cameras not connected. Camera1: {IsUSBCamera1Connected}, Camera2: {IsUSBCamera2Connected}";
                        return result;
                    }

                    // USB画像取得（既存のメソッドを使用）
                    var leftTexture = await GetUSBCameraImage(1);  // 左相機索引為1
                    var rightTexture = await GetUSBCameraImage(2); // 右相機索引為2

                    // 画像取得失敗チェック
                    if (leftTexture == null || rightTexture == null)
                    {
                        result.IsSuccess = false;
                        result.ErrorMessage = $"Failed to acquire USB camera images. Left: {leftTexture != null}, Right: {rightTexture != null}";
                        return result;
                    }

                    // 成功時のみCameraRespondData作成
                    result.LeftCameraImage = new CameraRespondData(0, leftTexture);
                    result.RightCameraImage = new CameraRespondData(1, rightTexture);
                }
                else
                {
                    result.LeftCameraImage = await GetImage(CameraPos.Left);
                    result.RightCameraImage = await GetImage(CameraPos.Right);
                }

                // AI検出器の初期化
                await InitializeBoardDetector();
                if (includeZoomAdjustment)
                {
                    await InitializeDartZoomPredictor();
                }

                // 両カメラの自動調整を並列実行
                var leftTask = PerformCameraAutoCalibrationAsync(
                    CameraPos.Left, includeZoomAdjustment, cancellationToken, result.LeftCameraImage.Texture);
                var rightTask = PerformCameraAutoCalibrationAsync(
                    CameraPos.Right, includeZoomAdjustment, cancellationToken, result.RightCameraImage.Texture);

                var calibrationResults = await Task.WhenAll(leftTask, rightTask);

                // 結果の収集
                var leftResult = calibrationResults[0];
                var rightResult = calibrationResults[1];

                if (!leftResult.IsSuccess || !rightResult.IsSuccess)
                {
                    result.IsSuccess = false;
                    result.LeftCameraPoints = leftResult.DetectedPoints;
                    result.RightCameraPoints = rightResult.DetectedPoints;
                    result.LeftCameraImage = new CameraRespondData(0, leftResult.Image);
                    result.RightCameraImage = new CameraRespondData(1, rightResult.Image);

                    result.ErrorMessage = $"Calibration failed. Left: {leftResult.ErrorMessage}, Right: {rightResult.ErrorMessage}";
                    return result;
                }

                result.LeftCameraPoints = leftResult.DetectedPoints;
                result.RightCameraPoints = rightResult.DetectedPoints;

                result.IsSuccess = true;
                return result;
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.ErrorMessage = ex.Message;
                return result;
            }
        }



        /// <summary>
        /// 単一カメラの自動キャリブレーション実行
        /// </summary>
        /// <param name="cameraPos">対象カメラ</param>
        /// <param name="includeZoomAdjustment">AI精密ズーム調整を含めるか</param>
        /// <param name="cancellationToken">キャンセレーション用トークン</param>
        /// <returns>単一カメラキャリブレーション結果</returns>
        private async Task<CameraCalibrationResult> PerformCameraAutoCalibrationAsync(
            CameraPos cameraPos,
            bool includeZoomAdjustment,
            CancellationToken cancellationToken,
            Texture cameraImage)
        {
            var result = new CameraCalibrationResult { Camera = cameraPos };

            try
            {
                Log.d($"Starting auto calibration for {cameraPos} camera...");

                // 4点検出
                var detectedPoints = await PredictBoard(cameraImage);
                if (detectedPoints == null || !detectedPoints.Any())
                {
                    throw new Exception($"No points detected for {cameraPos} camera");
                }

                // ベストポイントの選択と変換
                var bestPoints = SelectBestDetectedPoints(detectedPoints);
                var calibrationPoints = ConvertToCalibrationPoints(bestPoints, cameraPos);

                if (calibrationPoints.Count != 4)
                {
                    // 検出点不足の場合、部分結果を保存してエラーを返す
                    result.DetectedPoints = calibrationPoints;
                    result.Image = cameraImage; // 圖片也要保存
                    result.IsSuccess = false;
                    result.ErrorMessage = $"Insufficient points detected: {calibrationPoints.Count}/4";
                    Log.e($"Auto calibration failed for {cameraPos} camera: {result.ErrorMessage}");
                    return result;
                }

                // ズーム調整（オプション）
                if (includeZoomAdjustment)
                {
                    await PerformZoomAdjustmentAsync(cameraPos, cameraImage, calibrationPoints);
                }

                // 最終キャリブレーション実行
                var orderedPoints = calibrationPoints.OrderBy(p => p.Key)
                    .Select(p => p.Value).ToList();
                
                var isSuccess = Calibration(cameraPos, orderedPoints);
                if (!isSuccess)
                {
                    throw new Exception("Calibration matrix calculation failed");
                }

                result.DetectedPoints = calibrationPoints;
                result.IsSuccess = true;
                Log.d($"Auto calibration completed for {cameraPos} camera");

                return result;
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.ErrorMessage = ex.Message;
                Log.e($"Auto calibration failed for {cameraPos} camera: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// 検出されたポイントから最高スコアのポイントを選択
        /// </summary>
        /// <param name="detectedPoints">検出されたポイント一覧</param>
        /// <returns>最高スコアのポイント辞書</returns>
        private Dictionary<int, TFEntities.PredictResultPointEntity> SelectBestDetectedPoints(
            List<TFEntities.PredictResultPointEntity> detectedPoints)
        {
            var bestPoints = new Dictionary<int, TFEntities.PredictResultPointEntity>();
            
            foreach (var point in detectedPoints)
            {
                if (bestPoints.ContainsKey(point.Tag))
                {
                    if (bestPoints[point.Tag].score < point.score)
                        bestPoints[point.Tag] = point;
                }
                else
                {
                    bestPoints.Add(point.Tag, point);
                }
            }
            
            return bestPoints;
        }

        /// <summary>
        /// 検出されたポイントをキャリブレーション用座標に変換
        /// </summary>
        /// <param name="detectedPoints">検出されたポイント辞書</param>
        /// <param name="cameraPos">対象カメラ</param>
        /// <returns>キャリブレーション用座標辞書</returns>
        private Dictionary<int, Vector3> ConvertToCalibrationPoints(
            Dictionary<int, TFEntities.PredictResultPointEntity> detectedPoints, 
            CameraPos cameraPos)
        {
            var calibrationPoints = new Dictionary<int, Vector3>();
            
            foreach (var pointInfo in detectedPoints)
            {
                var pos = TflitePositionHelper.ModelPos2UnityPos(new Vector2(pointInfo.Value.x, pointInfo.Value.y));
                var pointIndex = pointInfo.Key - 1; // Tag 1-4 → Index 0-3
                
                Set_Dot(cameraPos, CalibrationType.Auto, pointIndex, pos);
                calibrationPoints.Add(pointInfo.Key, new Vector3(pos.x, pos.y, 0));
            }
            
            return calibrationPoints;
        }

        /// <summary>
        /// AI精密ズーム調整を実行
        /// </summary>
        /// <param name="cameraPos">対象カメラ</param>
        /// <param name="sourceTexture">元画像</param>
        /// <param name="pointsList">調整対象の点リスト</param>
        private async Task PerformZoomAdjustmentAsync(
            CameraPos cameraPos, 
            Texture sourceTexture, 
            Dictionary<int, Vector3> pointsList)
        {
            var calibrationTool = GetCalibrationTool(cameraPos);
            var adjustedPoints = new Dictionary<int, Vector3>();

            foreach (var pointInfo in pointsList)
            {
                try
                {
                    var adjustedPosition = await AdjustSinglePointAsync(
                        cameraPos, sourceTexture, calibrationTool, pointInfo);
                    adjustedPoints.Add(pointInfo.Key, adjustedPosition);
                }
                catch (Exception ex)
                {
                    Log.e($"Failed to adjust point {pointInfo.Key}: {ex.Message}");
                    // 個別点の調整に失敗した場合は元の位置を保持
                    adjustedPoints.Add(pointInfo.Key, pointInfo.Value);
                }
            }

            // 調整後の点で辞書を更新
            UpdatePointsList(pointsList, adjustedPoints);
        }

        /// <summary>
        /// 単一点のAI精密調整
        /// </summary>
        /// <param name="cameraPos">対象カメラ</param>
        /// <param name="sourceTexture">元画像</param>
        /// <param name="calibrationTool">キャリブレーションツール</param>
        /// <param name="pointInfo">調整対象の点情報</param>
        /// <returns>調整後の座標</returns>
        private async Task<Vector3> AdjustSinglePointAsync(
            CameraPos cameraPos, 
            Texture sourceTexture, 
            CalibrationTool calibrationTool, 
            KeyValuePair<int, Vector3> pointInfo)
        {
            var pointIndex = pointInfo.Key - 1; // Tag 1-4 → Index 0-3
            var centerPoint = new Vector2(pointInfo.Value.x, pointInfo.Value.y);
            
            // 1. ズーム画像を取得
            var zoomResult = GetZoomedImage(cameraPos, sourceTexture, calibrationTool, centerPoint);
            if (zoomResult.texture == null)
            {
                return pointInfo.Value;
            }
            
            // 2. AI予測を実行
            var aiPrediction = await GetAIPredictionAsync(zoomResult.texture);
            if (aiPrediction == null)
            {
                return pointInfo.Value;
            }
            
            // 3. 座標変換と調整
            var adjustedPosition = CalculateAdjustedPosition(
                aiPrediction, zoomResult, centerPoint, sourceTexture.width);
            
            // 4. 調整後の位置を保存
            Set_Dot(cameraPos, CalibrationType.Auto, pointIndex, adjustedPosition);
            
            return new Vector3(adjustedPosition.x, adjustedPosition.y, 0);
        }

        /// <summary>
        /// ズーム画像を取得
        /// </summary>
        /// <param name="cameraPos">対象カメラ</param>
        /// <param name="sourceTexture">元画像</param>
        /// <param name="calibrationTool">キャリブレーションツール</param>
        /// <param name="centerPoint">中心点</param>
        /// <returns>ズーム結果</returns>
        private ZoomResult GetZoomedImage(
            CameraPos cameraPos, 
            Texture sourceTexture, 
            CalibrationTool calibrationTool, 
            Vector2 centerPoint)
        {
            var virtualPoint = calibrationTool.GetVirtualPointFromReal(centerPoint);
            return GetZoomedTexture((int)cameraPos, sourceTexture, virtualPoint);
        }

        /// <summary>
        /// AI予測を実行
        /// </summary>
        /// <param name="zoomTexture">ズーム画像</param>
        /// <returns>予測結果</returns>
        private async Task<TFEntities.PredictResultPointEntity> GetAIPredictionAsync(Texture zoomTexture)
        {
            var predictResults = await PredictDartZoom(zoomTexture);
            return predictResults?.OrderByDescending(p => p.score).FirstOrDefault();
        }

        /// <summary>
        /// AI予測結果から調整後の座標を計算
        /// </summary>
        /// <param name="prediction">AI予測結果</param>
        /// <param name="zoomResult">ズーム結果</param>
        /// <param name="centerPoint">中心点</param>
        /// <param name="sourceWidth">元画像の幅</param>
        /// <returns>調整後の座標</returns>
        private Vector2 CalculateAdjustedPosition(
            TFEntities.PredictResultPointEntity prediction, 
            ZoomResult zoomResult, 
            Vector2 centerPoint, 
            int sourceWidth)
        {
            const float ZoomDisplaySize = 320f;
            
            // PagePointCheckModelViewと同じ座標変換ロジック
            var unityPos = TflitePositionHelper.ModelPos2UnityPos(new Vector2(prediction.x, prediction.y));
            var currentUIPosition = new Vector2(zoomResult.overflowX, zoomResult.overflowY);
            var aiPredictedOffset = new Vector2(unityPos.x * ZoomDisplaySize, unityPos.y * ZoomDisplaySize);
            var deltaOffset = aiPredictedOffset - currentUIPosition;
            
            // 正規化して最終座標を計算
            var normalizedOffsetX = deltaOffset.x / sourceWidth;
            var normalizedOffsetY = deltaOffset.y / sourceWidth;
            
            return new Vector2(
                centerPoint.x + normalizedOffsetX,
                centerPoint.y + normalizedOffsetY
            );
        }

        /// <summary>
        /// 点リストを更新
        /// </summary>
        /// <param name="originalPoints">元の点リスト</param>
        /// <param name="adjustedPoints">調整後の点リスト</param>
        private void UpdatePointsList(
            Dictionary<int, Vector3> originalPoints, 
            Dictionary<int, Vector3> adjustedPoints)
        {
            originalPoints.Clear();
            foreach (var adjustedPoint in adjustedPoints)
            {
                originalPoints.Add(adjustedPoint.Key, adjustedPoint.Value);
            }
        }

        #endregion
        #endregion
    }
}