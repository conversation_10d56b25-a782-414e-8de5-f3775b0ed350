using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using UnityEngine;
using com.luxza.granlog;

namespace com.luxza.graneye
{
    public partial class GranEyeCore
    {
        /// <summary>
        /// 投掷信息结构体
        /// </summary>
        private struct ThrowInfo
        {
            public int ThrowCount { get; set; }

            public ThrowInfo(int throwCount)
            {
                ThrowCount = throwCount;
            }
        }

        /// <summary>
        /// 拍照推理处理模式
        /// </summary>
        public enum InferenceMode
        {
            /// <summary>
            /// 单次模式：完成一次完整的拍照推理流程后才接受下一次拍照
            /// </summary>
            SingleProcess,

            /// <summary>
            /// 队列模式：推理过程中可以继续拍照并将图片加入待推理队列
            /// </summary>
            QueueProcess
        }

        #region 通用图像缓存相关变量与常量

        // 用于USB相机存储缓存图片的字典，键为相机索引，值为图片列表
        private readonly Dictionary<int, List<Texture2D>> _usbCachedImages = new Dictionary<int, List<Texture2D>>() {
            { 1, new List<Texture2D>() },
            { 2, new List<Texture2D>() }
        };

        /// <summary>
        /// 判断当前是否处于USB模式
        /// </summary>
        /// <returns>如果在USB模式返回true，否则返回false</returns>
        private bool IsUSBMode()
        {
            // 根据USB相机连接状态判断
            return IsUSBCamera1Connected || IsUSBCamera2Connected;
        }

        /// <summary>
        /// 将CameraPos枚举转换为USB相机索引
        /// </summary>
        /// <param name="pos">相机位置枚举</param>
        /// <returns>对应的USB相机索引</returns>
        private int ConvertCameraPosToUSBIndex(CameraPos pos)
        {
            switch (pos)
            {
                case CameraPos.Left:
                    return 1;  // USB相机1
                case CameraPos.Right:
                    return 2;  // USB相机2
                default:
                    return 0;  // 默认全部
            }
        }

        #endregion

        #region 照片推理管理

        // 简化为单个计数器，不需要额外的锁
        private int _throwCounter = 0;
        
        private readonly Dictionary<int, ImagePair> _imageCache = new();
        private readonly object _imageLock = new();
        
        private readonly Queue<ThrowInfo> _inferenceQueue = new();
        private bool _isProcessingInference = false;
        private InferenceMode _currentInferenceMode = InferenceMode.SingleProcess;
        private readonly SemaphoreSlim _queueSemaphore = new (1, 1);
        private Texture2D _leftCameraTexture;
        private Texture2D _rightCameraTexture;

        /// <summary>
        /// 推理完成事件
        /// </summary>
        public event Action<bool, List<CompareFilter.PredictWithBox>, PredictErrorCode> OnInferenceCompleted;

        /// <summary>
        /// 图像获取完成事件，传递当前投掷次数和图像
        /// </summary>
        public event Action<int, Texture> OnImageCaptured;

        /// <summary>
        /// 设置推理模式
        /// </summary>
        /// <param name="mode">推理模式</param>
        public void SetInferenceMode(InferenceMode mode)
        {
            _currentInferenceMode = mode;
            Log.d($"Current mode: {mode}");
        }

        private bool ShouldSwitchToQueueMode()
        {
            // 判断是否需要切换到队列模式
            // 1. 当前有正在处理的推理任务
            // 2. 队列中已有待处理的任务
            // 3. 图片缓存中有待处理的图片对
            lock (_imageLock)
            {
                return _isProcessingInference ||
                       _imageCache.Count > 0;
            }
        }

        #region 图片缓存队列管理
        private class ImagePair
        {
            public ThrowInfo ThrowInfo { get; set; }
            public Texture LeftImage { get; set; }
            public Texture RightImage { get; set; }
        }

        /// <summary>
        /// 添加图片到缓存 - 简化版
        /// </summary>
        /// <returns>返回创建的纹理副本，如果失败则返回null</returns>
        private Texture2D EnqueueImage(CameraPos pos, Texture texture, int throwCount)
        {
            if (texture == null) return null;
            
            // 创建纹理副本 - 简单方式
            Texture2D textureCopy = new Texture2D(texture.width, texture.height, TextureFormat.RGBA32, false);
            
            try
            {
                // 统一处理任何类型的纹理
                RenderTexture tempRT = RenderTexture.GetTemporary(texture.width, texture.height, 0);
                Graphics.Blit(texture, tempRT);
                
                RenderTexture prevActive = RenderTexture.active;
                RenderTexture.active = tempRT;
                textureCopy.ReadPixels(new Rect(0, 0, tempRT.width, tempRT.height), 0, 0);
                textureCopy.Apply();
                RenderTexture.active = prevActive;
                
                RenderTexture.ReleaseTemporary(tempRT);
                
                // 存储副本
                lock (_imageLock)
                {
                    // 清理旧纹理防止内存泄漏
                    if (_imageCache.TryGetValue(throwCount, out var existingPair))
                    {
                        if (pos == CameraPos.Left && existingPair.LeftImage != null && existingPair.LeftImage is Texture2D leftOldTexture)
                        {
                            UnityEngine.Object.Destroy(leftOldTexture);
                        }
                        else if (pos == CameraPos.Right && existingPair.RightImage != null && existingPair.RightImage is Texture2D rightOldTexture)
                        {
                            UnityEngine.Object.Destroy(rightOldTexture);
                        }
                    }
                    
                    if (!_imageCache.TryGetValue(throwCount, out var imagePair))
                    {
                        imagePair = new ImagePair { ThrowInfo = new ThrowInfo(throwCount) };
                        _imageCache[throwCount] = imagePair;
                    }
                    
                    if (pos == CameraPos.Left)
                    {
                        imagePair.LeftImage = textureCopy;
                    }
                    else
                    {
                        imagePair.RightImage = textureCopy;
                    }
                }
                
                return textureCopy;
            }
            catch (Exception ex)
            {
                Log.e($"Error caching image: {ex.Message}");
                UnityEngine.Object.Destroy(textureCopy);
                return null;
            }
        }

        /// <summary>
        /// 获取指定投掷次数的图片对
        /// </summary>
        private (Texture Left, Texture Right) GetImagePair(int throwCount)
        {
            lock (_imageLock)
            {
                if (_imageCache.TryGetValue(throwCount, out var imagePair))
                {
                    return (imagePair.LeftImage, imagePair.RightImage);
                }
                return (null, null);
            }
        }

        /// <summary>
        /// 清空图片缓存
        /// </summary>
        private void ClearImageQueue()
        {
            lock (_imageLock)
            {
                foreach (var imagePair in _imageCache.Values)
                {
                    if (imagePair.LeftImage != null && imagePair.LeftImage is Texture2D leftTexture)
                    {
                        UnityEngine.Object.Destroy(leftTexture);
                    }

                    if (imagePair.RightImage != null && imagePair.RightImage is Texture2D rightTexture)
                    {
                        UnityEngine.Object.Destroy(rightTexture);
                    }
                }

                _imageCache.Clear();
                Log.d("Image cache cleared");
            }
        }

        #endregion

        public void RevertThrowCount(int index)
        {
            _throwCounter = index;
        }

        /// <summary>
        /// 处理Echo震动，触发拍照推理流程
        /// </summary>
        public async Task<bool> ProcessEchoVibration()
        {
            // 简单地递增计数器并使用它作为唯一的throwCount
            _throwCounter++;
            var uniqueThrowCount = _throwCounter;
            // Original: 新しく振り分けたthrowCount: {uniqueThrowCount}
            Debug.Log($"Newly assigned throwCount: {uniqueThrowCount}");

            // 使用唯一的throwCount创建投掷信息
            var currentThrowInfo = new ThrowInfo(uniqueThrowCount);

            // 检查当前是否处于USB模式
            bool isUsbMode = IsUSBMode();

            // 第一次投掷，直接获取图像
            if (uniqueThrowCount == 1)
            {
                Debug.Log("１投目，获取图像");
                
                if (isUsbMode)
                {
                    // USB模式：使用直接从mRenderTextures获取图像方式
                    Debug.Log("使用USB相机模式处理第一次投掷 - 直接获取方式");
                    
                    // 使用优化后的USB相机图像获取方法 - 同时获取左右相机图像
                    var leftTextureTask = GetUSBCameraImage(1); // 左相机
                    var rightTextureTask = GetUSBCameraImage(2); // 右相机
                    
                    // 等待两个任务完成
                    await Task.WhenAll(leftTextureTask, rightTextureTask);
                    Texture leftTexture = await leftTextureTask;
                    Texture rightTexture = await rightTextureTask;
                    
                    // 先创建并保存到缓存，返回的是纹理副本
                    Texture2D leftTextureCopy = EnqueueImage(CameraPos.Left, leftTexture, currentThrowInfo.ThrowCount);
                    Texture2D rightTextureCopy = EnqueueImage(CameraPos.Right, rightTexture, currentThrowInfo.ThrowCount);
                    
                    if (leftTextureCopy != null)
                    {
                        Debug.Log($"左相机图像获取成功 - 尺寸: {leftTextureCopy.width}x{leftTextureCopy.height}");
                        // 触发图像获取完成事件，使用副本而不是原始图像
                        OnImageCaptured?.Invoke(currentThrowInfo.ThrowCount, leftTextureCopy);
                    }
                    else
                    {
                        Debug.LogError("左相机图像获取失败");
                    }
                    
                    if (rightTextureCopy == null)
                    {
                        Debug.LogError("右相机图像获取失败");
                    }
                }
                else
                {
                    // 网络相机模式：使用原有逻辑
                    Debug.Log("使用网络相机模式处理第一次投掷");
                    // 直接使用GetImage方法获取左右相机的图像
                    var leftImageTask = GetImage(CameraPos.Left);
                    var rightImageTask = GetImage(CameraPos.Right);

                    // 等待两个任务完成
                    await Task.WhenAll(leftImageTask, rightImageTask);
                    var leftImage = await leftImageTask;
                    var rightImage = await rightImageTask;

                    // Original: カメラ側から画像取得 - 左カメラ: {leftImage.IsSuccess}, 右カメラ: {rightImage.IsSuccess}
                    Log.d($"Images retrieved from cameras - Left camera: {leftImage.IsSuccess}, Right camera: {rightImage.IsSuccess}");

                    if (leftImage.IsSuccess && leftImage.Texture != null)
                    {
                        // Original: 左カメラ画像取得直後 - サイズ: {(leftImage.Texture as Texture2D).width}x{(leftImage.Texture as Texture2D).height}
                        Log.d($"Left camera image just retrieved - Size: {(leftImage.Texture as Texture2D).width}x{(leftImage.Texture as Texture2D).height}");
                    }
                    
                    // 将图片加入缓存队列
                    if (leftImage.IsSuccess)
                    {
                        Debug.Log($"左カメラEnqueueImage直前 - サイズ: {leftImage.Texture.width}x{leftImage.Texture.height}");
                        // 触发图像获取完成事件，传递投掷次数和左侧图像
                        OnImageCaptured?.Invoke(currentThrowInfo.ThrowCount, leftImage.Texture);
                    }
                    EnqueueImage(CameraPos.Left, leftImage.Texture, currentThrowInfo.ThrowCount);
                    EnqueueImage(CameraPos.Right, rightImage.Texture, currentThrowInfo.ThrowCount);
                }
            }
            else
            {
                // 检查是否有上一投的缓存
                var previousThrowCount = uniqueThrowCount - 1;
                var (previousLeftImage, previousRightImage) = GetImagePair(previousThrowCount);
                bool hasPreviousCache = previousLeftImage != null || previousRightImage != null;
                
                if (hasPreviousCache)
                {
                    Debug.Log($"前回の{previousThrowCount}投目のキャッシューが存在します");
                    
                    if (isUsbMode)
                    {
                        // USB模式：使用优化后的图像获取方法
                        Debug.Log("使用USB相机模式处理后续投掷（有缓存）");
                        
                        // 使用优化后的USB相机图像获取方法 - 同时获取左右相机图像
                        var leftTextureTask = GetUSBCameraImage(1); // 左相机
                        var rightTextureTask = GetUSBCameraImage(2); // 右相机
                        
                        // 等待两个任务完成
                        await Task.WhenAll(leftTextureTask, rightTextureTask);
                        Texture leftTexture = await leftTextureTask;
                        Texture rightTexture = await rightTextureTask;
                        
                        // 先创建并保存到缓存，返回的是纹理副本
                        Texture2D leftTextureCopy = EnqueueImage(CameraPos.Left, leftTexture, currentThrowInfo.ThrowCount);
                        Texture2D rightTextureCopy = EnqueueImage(CameraPos.Right, rightTexture, currentThrowInfo.ThrowCount);
                        
                        if (leftTextureCopy != null)
                        {
                            Debug.Log($"左相机图像获取成功 - 尺寸: {leftTextureCopy.width}x{leftTextureCopy.height}");
                            // 触发图像获取完成事件，使用副本而不是原始图像
                            OnImageCaptured?.Invoke(currentThrowInfo.ThrowCount, leftTextureCopy);
                        }
                        else
                        {
                            Debug.LogError("左相机图像获取失败");
                        }
                        
                        if (rightTextureCopy == null)
                        {
                            Debug.LogError("右相机图像获取失败");
                        }
                    }
                    else
                    {
                        // 网络相机模式：使用原有逻辑
                        Debug.Log("使用网络相机模式处理后续投掷（有缓存）");
                        // 有上一投的缓存，使用GetImage方法
                        var leftImageTask = GetImage(CameraPos.Left);
                        var rightImageTask = GetImage(CameraPos.Right);
                        
                        // 等待两个任务完成
                        await Task.WhenAll(leftImageTask, rightImageTask);
                        var leftImage = await leftImageTask;
                        var rightImage = await rightImageTask;
                        
                        Debug.Log($"カメラ側から画像取得 - 左カメラ: {leftImage.IsSuccess}, 右カメラ: {rightImage.IsSuccess}");
                        
                        if (leftImage.IsSuccess && leftImage.Texture != null)
                        {
                            Debug.Log($"左カメラ画像取得直後 - サイズ: {(leftImage.Texture as Texture2D).width}x{(leftImage.Texture as Texture2D).height}");
                        }
                        
                        // 将图片加入缓存队列
                        if (leftImage.IsSuccess)
                        {
                            Debug.Log($"左カメラEnqueueImage直前 - サイズ: {leftImage.Texture.width}x{leftImage.Texture.height}");
                            // 触发图像获取完成事件，传递投掷次数和左侧图像
                            OnImageCaptured?.Invoke(currentThrowInfo.ThrowCount, leftImage.Texture);
                        }
                        EnqueueImage(CameraPos.Left, leftImage.Texture, currentThrowInfo.ThrowCount);
                        EnqueueImage(CameraPos.Right, rightImage.Texture, currentThrowInfo.ThrowCount);
                    }
                }
                else
                {
                    Debug.Log($"前回の{previousThrowCount}投目のキャッシューが存在しません");
                    
                    if (isUsbMode)
                    {
                        // USB模式：使用优化后的图像获取方法
                        Debug.Log("使用USB相机模式处理后续投掷（无缓存）");
                        
                        // 向相机发送存储图像命令
                        bool leftStoreResult = SendStoreImageCommand(1);
                        bool rightStoreResult = SendStoreImageCommand(2);
                        
                        if (!leftStoreResult || !rightStoreResult)
                        {
                            Debug.LogError($"USB相机存储命令发送失败 - 左相机: {leftStoreResult}, 右相机: {rightStoreResult}");
                        }
                        
                        // 使用优化后的USB相机图像获取方法 - 同时获取左右相机存储的图像
                        Texture leftTexture = GetUSBStoredImage(1); // 左相机
                        Texture rightTexture = GetUSBStoredImage(2); // 右相机
                        
                        // 先创建并保存到缓存，返回的是纹理副本
                        Texture2D leftTextureCopy = EnqueueImage(CameraPos.Left, leftTexture, currentThrowInfo.ThrowCount);
                        Texture2D rightTextureCopy = EnqueueImage(CameraPos.Right, rightTexture, currentThrowInfo.ThrowCount);
                        
                        if (leftTextureCopy != null)
                        {
                            Debug.Log($"左相机图像获取成功 - 尺寸: {leftTextureCopy.width}x{leftTextureCopy.height}");
                            // 触发图像获取完成事件，使用副本而不是原始图像
                            OnImageCaptured?.Invoke(currentThrowInfo.ThrowCount, leftTextureCopy);
                        }
                        else
                        {
                            Debug.LogError("左相机图像获取失败");
                        }
                        
                        if (rightTextureCopy == null)
                        {
                            Debug.LogError("右相机图像获取失败");
                        }
                    }
                    else
                    {
                        // 网络相机模式：使用原有逻辑
                        Debug.Log("使用网络相机模式处理后续投掷（无缓存）");
                        // 没有上一投的缓存，使用原有逻辑
                        // 使用更新后的throwCount进行存储
                        var leftStoreTask = StoreImage();
                        var rightStoreTask = StoreImage(CameraPos.Right);
                        
                        // 等待两个任务完成
                        await Task.WhenAll(leftStoreTask, rightStoreTask);
                        var leftStoreResult = await leftStoreTask;
                        var rightStoreResult = await rightStoreTask;

                        if (!leftStoreResult || !rightStoreResult)
                        {
                            Debug.LogError($"カメラ側キャシュー保存失敗 - 左カメラ: {leftStoreResult}, 右カメラ: {rightStoreResult}");
                        }

                        Debug.Log($"カメラ側キャシュー保存成功 - 左カメラ: {true}, 右カメラ: {true}");

                        // 给一个短暂的延迟，确保图片已经完全存储
                        await Task.Delay(50);
                        
                        // 并行执行左右相机的获取操作
                        var leftImageTask = GetStoredImage();
                        var rightImageTask = GetStoredImage(CameraPos.Right);
                        
                        // 等待两个任务完成
                        await Task.WhenAll(leftImageTask, rightImageTask);
                        var leftImage = await leftImageTask;
                        var rightImage = await rightImageTask;

                        Debug.Log($"カメラ側からキャシュー受信 - 左カメラ: {leftImage.IsSuccess}, 右カメラ: {rightImage.IsSuccess}");
                        
                        if (leftImage.IsSuccess && leftImage.Texture != null)
                        {
                            Debug.Log($"左カメラ画像取得直後 - サイズ: {(leftImage.Texture as Texture2D).width}x{(leftImage.Texture as Texture2D).height}");
                        }
                        // 将图片加入缓存队列
                        if (leftImage.IsSuccess)
                        {
                            Debug.Log($"左カメラEnqueueImage直前 - サイズ: {leftImage.Texture.width}x{leftImage.Texture.height}");
                            // 触发图像获取完成事件，传递投掷次数和左侧图像
                            OnImageCaptured?.Invoke(currentThrowInfo.ThrowCount, leftImage.Texture);
                        }
                        EnqueueImage(CameraPos.Left, leftImage.Texture, currentThrowInfo.ThrowCount);
                        EnqueueImage(CameraPos.Right, rightImage.Texture, currentThrowInfo.ThrowCount);
                    }
                }
            }

            // 根据当前状态决定是否切换模式
            var shouldUseQueue = ShouldSwitchToQueueMode();
            SetInferenceMode(shouldUseQueue ? InferenceMode.QueueProcess : InferenceMode.SingleProcess);

            if (_currentInferenceMode == InferenceMode.SingleProcess && !_isProcessingInference)
            {
                // 单次模式且当前没有处理中的任务，直接进行推理
                _isProcessingInference = true;

                try
                {
                    // 从缓存中获取图片对
                    var (leftTexture, rightTexture) = GetImagePair(currentThrowInfo.ThrowCount);

                    if (leftTexture == null || rightTexture == null)
                    {
                        // Original: ローカルイメージ取得失敗 - 左カメラ: {leftTexture != null}, 右カメラ: {rightTexture != null}
                        Log.e($"Local image retrieval failed - Left camera: {leftTexture != null}, Right camera: {rightTexture != null}");
                    }

                    var textures = new Dictionary<CameraPos, Texture>
                    {
                        { CameraPos.Left, leftTexture },
                        { CameraPos.Right, rightTexture }
                    };

                    // 执行推理
                    var (isFixed, predictList, errorCode) = await PredictWithTexture(currentThrowInfo.ThrowCount, textures, false);
                    OnInferenceCompleted?.Invoke(isFixed, predictList, errorCode);
                    return true;
                }
                finally
                {
                    _isProcessingInference = false;
                }
            }
            else
            {
                // 将投镖信息加入推理队列
                await _queueSemaphore.WaitAsync();
                try
                {
                    _inferenceQueue.Enqueue(currentThrowInfo);
                    // Original: {currentThrowInfo.ThrowCount}目既に配列に入れている，該当配列数: {_inferenceQueue.Count}
                    Log.d($"Throw #{currentThrowInfo.ThrowCount} already added to queue, current queue count: {_inferenceQueue.Count}");

                    // 如果没有正在处理的推理，启动处理
                    if (!_isProcessingInference)
                    {
                        _ = ProcessInferenceQueue();
                    }
                    else
                    {
                        // Original: 現在推論中です。{currentThrowInfo.ThrowCount} 目は待っている
                        Log.d($"Currently inferencing. Throw #{currentThrowInfo.ThrowCount} is waiting");
                    }

                    return true;
                }
                finally
                {
                    _queueSemaphore.Release();
                }
            }
        }

        /// <summary>
        /// 处理推理队列
        /// </summary>
        private async Task ProcessInferenceQueue()
        {
            _isProcessingInference = true;

            try
            {
                while (_inferenceQueue.Count > 0)
                {
                    var currentThrowInfo = _inferenceQueue.Dequeue();
                    // 从缓存中获取图片对
                    var (leftImage, rightImage) = GetImagePair(currentThrowInfo.ThrowCount);

                    if (leftImage == null || rightImage == null)
                    {
                        // Original: ローカルイメージ取得失敗 - 左カメラ: {leftImage != null}, 右カメラ: {rightImage != null}
                        Log.e($"Local image retrieval failed - Left camera: {leftImage != null}, Right camera: {rightImage != null}");
                    }

                    var textures = new Dictionary<CameraPos, Texture>
                    {
                        { CameraPos.Left, leftImage },
                        { CameraPos.Right, rightImage }
                    };

                    // 执行推理
                    var (isFixed, predictList, errorCode) = await PredictWithTexture(currentThrowInfo.ThrowCount, textures, false);
                    
                    // 触发推理完成事件
                    OnInferenceCompleted?.Invoke(isFixed, predictList, errorCode);

                    // 注意：现在我们不销毁图片，因为它们会保留在缓存中
                }
            }
            catch (OperationCanceledException)
            {
                // Original: 推理处理被取消
                Log.d("Inference processing cancelled");
            }
            catch (Exception ex)
            {
                // Original: 处理推理队列时出错: {ex.Message}
                Log.e($"Error processing inference queue: {ex.Message}");
            }
            finally
            {
                _isProcessingInference = false;
            }
        }

        /// <summary>
        /// 清空推理队列
        /// </summary>
        public async Task ClearInferenceQueue()
        {
            await _queueSemaphore.WaitAsync();
            try
            {
                // 重置投掷计数器
                _throwCounter = 0;
                // Original: 投掷计数器已重置为0
                Log.d("Throw counter reset to 0");

                _inferenceQueue.Clear();

                // 清理缓存的图片
                ClearImageQueue();
                _ = CleanImageCaches();
                _ = CleanImageCaches(CameraPos.Right);
            }
            finally
            {
                _queueSemaphore.Release();
            }
        }

        #endregion

        #region 图片缓存管理

        /// <summary>
        /// 获取已缓存图片的数量
        /// </summary>
        /// <param name="pos">相机位置</param>
        /// <returns>缓存图片数量</returns>
        public Task<string> GetStoredImageCount(CameraPos pos = CameraPos.Left)
        {
            // 如果是USB模式，调用USB相机的方法
            if (IsUSBMode())
            {
                var count = GetUSBStoredCount(ConvertCameraPosToUSBIndex(pos));
                return Task.FromResult(count.ToString());
            }
            
            // 否则调用网络相机的方法
            try
            {
                var camera = GetCamera(pos);
                return camera.GetStoredImageCount();
            }
            catch (Exception ex)
            {
                // Original: 获取已缓存图片数量时出错: {ex.Message}
                Log.e($"Error getting stored image count: {ex.Message}");
                return Task.FromResult(string.Empty);
            }
        }

        /// <summary>
        /// 缓存当前图片
        /// </summary>
        /// <param name="pos">相机位置</param>
        /// <returns>操作是否成功</returns>
        private async Task<bool> StoreImage(CameraPos pos = CameraPos.Left)
        {
            // 如果是USB模式，调用USB相机的方法
            if (IsUSBMode())
            {
                return StoreUSBImage(ConvertCameraPosToUSBIndex(pos));
            }
            
            // 否则调用网络相机的方法
            try
            {
                if (!CheckCameraUsable(pos))
                {
                    // Original: 相机 {pos} 不可用，无法缓存图片
                    Debug.LogWarning($"Camera {pos} not available, cannot store image");
                    return false;
                }

                var camera = GetCamera(pos);
                return await camera.StoreImage();
            }
            catch (Exception ex)
            {
                // Original: 缓存图片时出错: {ex.Message}
                Log.e($"Error storing image: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取已缓存的图片
        /// </summary>
        /// <param name="pos">相机位置</param>
        /// <returns>相机响应数据</returns>
        private async Task<CameraRespondData> GetStoredImage(CameraPos pos = CameraPos.Left)
        {
            // 如果是USB模式，调用USB相机的方法
            if (IsUSBMode())
            {
                var texture = GetUSBStoredImage(ConvertCameraPosToUSBIndex(pos));
                if (texture != null)
                {
                    return new CameraRespondData((int)pos, texture);
                }
                return new CameraRespondData();
            }
            
            // 否则调用网络相机的方法
            if (!CheckCameraUsable(pos)) return new CameraRespondData();

            pendingTasks.TryAdd((int)pos, new TaskWithResult<CameraRespondData>());
            var t_Result = false;
            try
            {
                var camera = GetCamera(pos);
                t_Result = await camera.GetStoredImage().TimeoutAfter(TimeSpan.FromSeconds(5));
            }
            catch (TimeoutException)
            {
                pendingTasks.TryRemove((int)pos, out _);
                return new CameraRespondData();
            }
            catch (Exception ex)
            {
                // Original: 获取缓存图片时出错: {ex.Message}
                Log.e($"Error retrieving stored image: {ex.Message}");
                pendingTasks.TryRemove((int)pos, out _);
                return new CameraRespondData();
            }

            if (t_Result)
            {
                var taskWithResult = pendingTasks.GetOrAdd((int)pos, _ => new TaskWithResult<CameraRespondData>());
                return await taskWithResult.TaskCompletionSource.Task;
            }

            return new CameraRespondData();
        }

        /// <summary>
        /// 清除所有图片缓存
        /// </summary>
        /// <param name="pos">相机位置</param>
        /// <returns>操作是否成功</returns>
        private async Task<bool> CleanImageCaches(CameraPos pos = CameraPos.Left)
        {
            // 如果是USB模式，调用USB相机的方法
            if (IsUSBMode())
            {
                return CleanUSBImageCaches(ConvertCameraPosToUSBIndex(pos));
            }
            
            // 否则调用网络相机的方法
            try
            {
                if (!CheckCameraUsable(pos))
                {
                    Debug.LogError($"相机 {pos} 不可用，无法清除图片缓存");
                    return false;
                }

                var camera = GetCamera(pos);
                return await camera.CleanImageCaches();
            }
            catch (Exception ex)
            {
                // Original: 清除图片缓存时出错: {ex.Message}
                Log.e($"Error clearing image cache: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region USB相机图片缓存实现

        /// <summary>
        /// 获取USB相机图像，确保获取完全更新的图像
        /// </summary>
        /// <param name="cameraIndex">相机索引：1表示左相机，2表示右相机</param>
        /// <returns>获取的图像纹理</returns>
        public async Task<Texture> GetUSBCameraImage(int cameraIndex)
        {
            // 检查相机连接状态
            if ((cameraIndex == 1 && !IsUSBCamera1Connected) || 
                (cameraIndex == 2 && !IsUSBCamera2Connected))
            {
                Debug.LogError($"相机{cameraIndex}未连接，无法获取图像");
                return null;
            }
            
            // 发送拍照命令
            Debug.Log($"向相机{cameraIndex}发送拍照命令");
            GetPictureSingle(cameraIndex);
            
            // 创建TaskCompletionSource等待图像就绪
            var imageReceived = new TaskCompletionSource<Texture>();
            
            // 根据相机索引选择正确的事件
            Action<Texture> imageReadyHandler = null;
            
            if (cameraIndex == 1)
            {
                imageReadyHandler = texture => {
                    OnCamera1ImageReady -= imageReadyHandler;
                    imageReceived.TrySetResult(texture);
                };
                
                // 订阅Camera1ImageReady事件
                OnCamera1ImageReady += imageReadyHandler;
            }
            else if (cameraIndex == 2)
            {
                imageReadyHandler = texture => {
                    OnCamera2ImageReady -= imageReadyHandler;
                    imageReceived.TrySetResult(texture);
                };
                
                // 订阅Camera2ImageReady事件
                OnCamera2ImageReady += imageReadyHandler;
            }
            
            try
            {
                // 等待图像就绪，设置合理的超时时间（比如1秒）
                return await imageReceived.Task.TimeoutAfter(TimeSpan.FromSeconds(0.5));
            }
            catch (TimeoutException)
            {
                Debug.LogWarning($"等待相机{cameraIndex}图像更新超时");
                
                // 超时时取消事件订阅
                if (cameraIndex == 1)
                {
                    OnCamera1ImageReady -= imageReadyHandler;
                }
                else if (cameraIndex == 2)
                {
                    OnCamera2ImageReady -= imageReadyHandler;
                }
                
                // 超时后返回当前渲染纹理
                return mRenderTextures[cameraIndex - 1];
            }
        }

        /// <summary>
        /// 为USB相机缓存当前图片
        /// </summary>
        /// <param name="cameraIndex">相机索引：1表示第一个相机，2表示第二个相机</param>
        /// <returns>是否成功缓存图片</returns>
        private bool StoreUSBImage(int cameraIndex)
        {
            // 检查相机连接状态
            if ((cameraIndex == 1 && !IsUSBCamera1Connected) || 
                (cameraIndex == 2 && !IsUSBCamera2Connected))
            {
                Debug.LogError($"相机{cameraIndex}未连接，无法缓存图片");
                return false;
            }
            
            try
            {
                // 获取当前RenderTexture
                RenderTexture currentRenderTexture = mRenderTextures[cameraIndex - 1];
                
                // 创建Texture2D并从RenderTexture读取像素
                Texture2D textureCopy = new Texture2D(currentRenderTexture.width, currentRenderTexture.height, TextureFormat.RGBA32, false);
                RenderTexture.active = currentRenderTexture;
                textureCopy.ReadPixels(new Rect(0, 0, currentRenderTexture.width, currentRenderTexture.height), 0, 0);
                textureCopy.Apply();
                RenderTexture.active = null;
                
                // 确保列表存在
                if (!_usbCachedImages.ContainsKey(cameraIndex))
                {
                    _usbCachedImages[cameraIndex] = new List<Texture2D>();
                }
                
                // 添加到缓存
                _usbCachedImages[cameraIndex].Add(textureCopy);
                
                Debug.Log($"已缓存相机{cameraIndex}的图片，当前缓存数量: {_usbCachedImages[cameraIndex].Count}");
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"缓存相机{cameraIndex}图片时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取USB相机已缓存的图片
        /// </summary>
        /// <param name="cameraIndex">相机索引：1表示第一个相机，2表示第二个相机</param>
        /// <param name="imageIndex">图片索引，从0开始</param>
        /// <returns>缓存的图片，如果不存在则返回null</returns>
        private Texture2D GetUSBStoredImage(int cameraIndex, int imageIndex = -1)
        {
            // 确保列表存在
            if (!_usbCachedImages.ContainsKey(cameraIndex) || _usbCachedImages[cameraIndex].Count == 0)
            {
                Debug.LogError($"相机{cameraIndex}没有缓存的图片");
                return null;
            }
            
            // 如果索引为-1，则返回最新的图片
            if (imageIndex == -1)
            {
                imageIndex = _usbCachedImages[cameraIndex].Count - 1;
            }
            
            // 检查索引是否有效
            if (imageIndex < 0 || imageIndex >= _usbCachedImages[cameraIndex].Count)
            {
                Debug.LogError($"无效的图片索引: {imageIndex}，有效范围: 0-{_usbCachedImages[cameraIndex].Count - 1}");
                return null;
            }
            
            return _usbCachedImages[cameraIndex][imageIndex]; 
        }

        /// <summary>
        /// 清除所有USB相机缓存的图片
        /// </summary>
        /// <param name="cameraIndex">相机索引：1表示第一个相机，2表示第二个相机，0表示所有相机</param>
        /// <returns>是否成功清除缓存</returns>
        private bool CleanUSBImageCaches(int cameraIndex = 0)
        {
            try
            {
                if (cameraIndex == 0)
                {
                    // 清除所有相机的缓存
                    foreach (var camIndex in _usbCachedImages.Keys.ToList())
                    {
                        foreach (var texture in _usbCachedImages[camIndex])
                        {
                            UnityEngine.Object.Destroy(texture);
                        }
                        _usbCachedImages[camIndex].Clear();
                    }
                    Debug.Log("已清除所有USB相机的图片缓存");
                }
                else if (cameraIndex == 1 || cameraIndex == 2)
                {
                    // 确保列表存在
                    if (_usbCachedImages.ContainsKey(cameraIndex))
                    {
                        // 销毁所有纹理以防止内存泄漏
                        foreach (var texture in _usbCachedImages[cameraIndex])
                        {
                            UnityEngine.Object.Destroy(texture);
                        }
                        _usbCachedImages[cameraIndex].Clear();
                        Debug.Log($"已清除USB相机{cameraIndex}的图片缓存");
                    }
                }
                else
                {
                    Debug.LogError($"无效的相机索引: {cameraIndex}，必须是0、1或2");
                    return false;
                }
                
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"清除USB图片缓存时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取指定USB相机缓存的图片数量
        /// </summary>
        /// <param name="cameraIndex">相机索引：1表示第一个相机，2表示第二个相机</param>
        /// <returns>缓存的图片数量</returns>
        private int GetUSBStoredCount(int cameraIndex)
        {
            // 确保索引有效
            if (cameraIndex != 1 && cameraIndex != 2)
            {
                Debug.LogError($"无效的相机索引: {cameraIndex}，必须是1或2");
                return 0;
            }
            
            // 确保列表存在
            if (!_usbCachedImages.ContainsKey(cameraIndex))
            {
                return 0;
            }
            
            return _usbCachedImages[cameraIndex].Count;
        }

        /// <summary>
        /// 向USB相机发送单独获取图片命令
        /// </summary>
        /// <param name="cameraIndex">相机索引：1表示第一个相机，2表示第二个相机</param>
        /// <returns>是否成功发送命令</returns>
        private bool SendGetPictureSingleCommand(int cameraIndex)
        {
            try
            {
                // 检查相机连接状态
                if ((cameraIndex == 1 && !IsUSBCamera1Connected) || 
                    (cameraIndex == 2 && !IsUSBCamera2Connected))
                {
                    Debug.LogError($"设备{cameraIndex}未连接，无法发送命令");
                    return false;
                }
                
                // 发送获取图片命令
                this.GranUSBClients[cameraIndex].Send("picture");
                Debug.Log($"已向设备{cameraIndex}发送获取图片命令");
                
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"发送获取图片命令时发生错误: {ex.Message}");
                return false;
            }
        }

        #endregion
    }
}