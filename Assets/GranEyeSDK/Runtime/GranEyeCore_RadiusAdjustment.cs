using System;
using System.Collections.Generic;
using UnityEngine;

namespace com.luxza.graneye
{
    /// <summary>
    /// Calibration point display information
    /// </summary>
    [Serializable]
    public struct CalibrationPointDisplayInfo
    {
        public Vector3 Position;
        public Color Color;
        public bool IsVisible;
        public float Alpha;

        public CalibrationPointDisplayInfo(Vector3 position, Color color, bool isVisible = true, float alpha = 1f)
        {
            Position = position;
            Color = color;
            IsVisible = isVisible;
            Alpha = alpha;
        }
    }

    /// <summary>
    /// Zoom and scale information for camera view
    /// </summary>
    [Serializable]
    public struct ZoomInfo
    {
        public Vector2 CenterPoint;
        public float Scale;
        public Vector2 Offset;
        public bool IsZoomed;

        public ZoomInfo(Vector2 centerPoint, float scale, Vector2 offset, bool isZoomed = true)
        {
            CenterPoint = centerPoint;
            Scale = scale;
            Offset = offset;
            IsZoomed = isZoomed;
        }

        public static ZoomInfo Default => new(Vector2.zero, 1f, Vector2.zero, false);
    }

    /// <summary>
    /// Radius adjustment functionality for GranEyeCore
    /// Acts as a bridge between UI and core GranEye functionality
    /// </summary>
    public static class GranEyeRadiusAdjustment
    {
        #region Events

        /// <summary>
        /// Event triggered when calibration points need to be updated
        /// </summary>
        public static event Action<GranEyeCore.CameraPos, List<CalibrationPointDisplayInfo>> OnCalibrationPointsUpdated;

        /// <summary>
        /// Event triggered when zoom state should be changed
        /// </summary>
        public static event Action<GranEyeCore.CameraPos, ZoomInfo> OnZoomStateChanged;

        /// <summary>
        /// Event triggered when radius adjustment operation starts
        /// </summary>
        public static event Action<GranEyeCore.CameraPos, CustomBoardRadiusType> OnRadiusAdjustmentStarted;

        #endregion

        #region State Management

        private static readonly Dictionary<GranEyeCore.CameraPos, RadiusAdjustmentState> _adjustmentStates = new();

        private struct RadiusAdjustmentState
        {
            public CustomBoardRadiusType? SelectedRadiusType;
            public bool IsShowingAllPoints;
            public bool IsCustomizationMode;
        }

        #endregion

        #region Public API

        /// <summary>
        /// Initialize radius adjustment for specified camera
        /// </summary>
        public static void InitializeRadiusAdjustment(GranEyeCore.CameraPos cameraPos)
        {
            _adjustmentStates[cameraPos] = new RadiusAdjustmentState
            {
                SelectedRadiusType = null,
                IsShowingAllPoints = false,
                IsCustomizationMode = false
            };

            // Trigger initial points update using delegate
            RefreshCalibrationPointsDisplay(cameraPos);
        }

        /// <summary>
        /// Set selected radius type for adjustment
        /// </summary>
        public static void SetSelectedRadiusType(GranEyeCore.CameraPos cameraPos, CustomBoardRadiusType? radiusType)
        {
            if (!_adjustmentStates.ContainsKey(cameraPos))
                InitializeRadiusAdjustment(cameraPos);

            var state = _adjustmentStates[cameraPos];

            if (radiusType.HasValue)
            {
                state.SelectedRadiusType = radiusType;
                state.IsShowingAllPoints = false;

                // Start customization mode if needed (delegate to external logic)
                if (!state.IsCustomizationMode)
                {
                    state.IsCustomizationMode = true;
                    // External code should handle customization start
                }

                _adjustmentStates[cameraPos] = state;

                // Trigger zoom to selected area
                var zoomInfo = CalculateZoomInfo(cameraPos, radiusType.Value);
                OnZoomStateChanged?.Invoke(cameraPos, zoomInfo);

                // Trigger radius adjustment started event
                OnRadiusAdjustmentStarted?.Invoke(cameraPos, radiusType.Value);
            }
            else
            {
                // Reset to initial state
                state.SelectedRadiusType = null;
                state.IsShowingAllPoints = false;
                _adjustmentStates[cameraPos] = state;

                // Reset zoom
                OnZoomStateChanged?.Invoke(cameraPos, ZoomInfo.Default);
            }

            // Refresh point display
            RefreshCalibrationPointsDisplay(cameraPos);
        }

        /// <summary>
        /// Perform radius adjustment using delegate
        /// </summary>
        public static void PerformRadiusAdjustment(GranEyeCore.CameraPos cameraPos, float delta, 
            System.Func<CustomBoardRadiusType, float, bool> adjustRadiusDelegate)
        {
            if (!_adjustmentStates.ContainsKey(cameraPos) || !_adjustmentStates[cameraPos].SelectedRadiusType.HasValue)
                return;

            var state = _adjustmentStates[cameraPos];
            
            // Mark as customization mode
            state.IsCustomizationMode = true;
            state.IsShowingAllPoints = false;
            _adjustmentStates[cameraPos] = state;

            // Perform actual radius adjustment using delegate
            if (adjustRadiusDelegate != null)
            {
                adjustRadiusDelegate(state.SelectedRadiusType.Value, delta);
            }

            // Refresh points (showing only selected area)
            RefreshCalibrationPointsDisplay(cameraPos);
        }

        /// <summary>
        /// Set show all points flag
        /// </summary>
        public static void SetShowAllPoints(GranEyeCore.CameraPos cameraPos, bool showAll)
        {
            if (!_adjustmentStates.ContainsKey(cameraPos))
                return;

            var state = _adjustmentStates[cameraPos];
            state.IsShowingAllPoints = showAll;
            _adjustmentStates[cameraPos] = state;

            RefreshCalibrationPointsDisplay(cameraPos);
        }

        /// <summary>
        /// Cleanup radius adjustment for specified camera
        /// </summary>
        public static void CleanupRadiusAdjustment(GranEyeCore.CameraPos cameraPos)
        {
            _adjustmentStates.Remove(cameraPos);
        }

        /// <summary>
        /// Update calibration points using external data provider
        /// </summary>
        public static void UpdateCalibrationPoints(GranEyeCore.CameraPos cameraPos, List<Vector3> points)
        {
            var displayInfo = new List<CalibrationPointDisplayInfo>();

            if (!_adjustmentStates.ContainsKey(cameraPos))
            {
                // Default display: all points visible
                foreach (var point in points)
                {
                    displayInfo.Add(new CalibrationPointDisplayInfo(point, Color.yellow, true, 1f));
                }
            }
            else
            {
                var state = _adjustmentStates[cameraPos];
                
                for (int i = 0; i < points.Count; i++)
                {
                    var color = CalculatePointColor(i, state);
                    displayInfo.Add(new CalibrationPointDisplayInfo(points[i], color.color, true, color.alpha));
                }
            }

            OnCalibrationPointsUpdated?.Invoke(cameraPos, displayInfo);
        }

        #endregion

        #region Private Methods

        private static void RefreshCalibrationPointsDisplay(GranEyeCore.CameraPos cameraPos)
        {
            // This method will be called by external code that has access to GranEye instance
            // For now, trigger event with empty list - external code should call UpdateCalibrationPoints
            OnCalibrationPointsUpdated?.Invoke(cameraPos, new List<CalibrationPointDisplayInfo>());
        }

        private static (Color color, float alpha) CalculatePointColor(int pointIndex, RadiusAdjustmentState state)
        {
            Color baseColor = Color.yellow;

            // If no area selected or showing all points, display normal transparency
            if (!state.SelectedRadiusType.HasValue || state.IsShowingAllPoints)
            {
                return (baseColor, 1f);
            }

            // Get selected circle index
            int selectedCircleIndex = GetSelectedCircleIndex(state.SelectedRadiusType.Value);

            // Calculate which circle current point belongs to (20 points per circle)
            int currentCircleIndex = pointIndex / 20;

            // If it's selected circle, show normal transparency; otherwise show 5% transparency
            float alpha = (currentCircleIndex == selectedCircleIndex) ? 1f : 0.05f;
            return (baseColor, alpha);
        }

        private static int GetSelectedCircleIndex(CustomBoardRadiusType radiusType)
        {
            return radiusType switch
            {
                CustomBoardRadiusType.DoubleRadius => 3,      // Double outer -> Circle 3
                CustomBoardRadiusType.TripleRadius => 1,      // Triple outer -> Circle 1
                CustomBoardRadiusType.SingleInRadius => 0,    // Triple inner -> Circle 0
                CustomBoardRadiusType.OuterBullRadius => 5,   // Outer Bull -> Circle 5
                CustomBoardRadiusType.InnerBullRadius => 4,   // Inner Bull -> Circle 4
                _ => -1 // Invalid selection
            };
        }

        private static ZoomInfo CalculateZoomInfo(GranEyeCore.CameraPos cameraPos, CustomBoardRadiusType radiusType)
        {
            var centerPoint = GetAreaCenterPoint(cameraPos, radiusType);
            float zoomScale = 2.5f;

            return new ZoomInfo(centerPoint, zoomScale, Vector2.zero, true);
        }

        /// <summary>
        /// Get area center point for zoom - will be calculated from actual points data
        /// </summary>
        public static Vector2 CalculateAreaCenterPoint(List<Vector3> allPoints, CustomBoardRadiusType radiusType)
        {
            int circleIndex = radiusType switch
            {
                CustomBoardRadiusType.DoubleRadius => 3,     // Double outer -> Circle 3
                CustomBoardRadiusType.TripleRadius => 1,     // Triple outer -> Circle 1
                CustomBoardRadiusType.SingleInRadius => 0,   // Triple inner -> Circle 0
                CustomBoardRadiusType.OuterBullRadius => 5,  // Outer Bull -> Circle 5
                CustomBoardRadiusType.InnerBullRadius => 4,  // Inner Bull -> Circle 4
                _ => -1
            };

            if (circleIndex < 0 || allPoints == null) return Vector2.zero;

            // Select first point of that circle as center point (index = circleIndex * 20)
            int pointIndex = circleIndex * 20;
            if (pointIndex < allPoints.Count)
            {
                var point3D = allPoints[pointIndex];
                return new Vector2(point3D.x, point3D.y);
            }

            return Vector2.zero;
        }

        private static Vector2 GetAreaCenterPoint(GranEyeCore.CameraPos cameraPos, CustomBoardRadiusType radiusType)
        {
            // Return zero for now - will be updated when actual points are provided
            return Vector2.zero;
        }

        #endregion
    }
}