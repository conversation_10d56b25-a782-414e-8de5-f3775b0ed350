namespace com.luxza.grandartslogic.domain.game.shanghai
{
    public class GameRuleShangHai : IGameRule
    {
        public GameCode? Code { get; } = GameCode._ShangHai;
        public int MaxRound { get; }
        public int ThrowsPerRound { get; }
        public int MaximumNumberOfParticipants { get; }
        public int[] HalfRoundOption = new int[20];
        public bool IsPlayUntilMaxRound = false;
        
        public GameRuleShangHai
        (
            int[] halfRoundOption,
            int maxRound = 20,
            int throwsPerRound = 3,
            int maximumNumberOfParticipants = 4,
            bool isPlayUntilMaxRound = false
        )
        {
            HalfRoundOption = halfRoundOption;
            MaxRound = maxRound;
            ThrowsPerRound = throwsPerRound;
            MaximumNumberOfParticipants = maximumNumberOfParticipants;
            IsPlayUntilMaxRound = isPlayUntilMaxRound;
        }

        public static readonly Award[] AchievableAwards = new Award[]
        {
            
        };
    }
}