using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using com.luxza.grandartslogic.domain.award;
using com.luxza.grandartslogic.domain.game.round;
using com.luxza.grandartslogic.domain.service;
using com.luxza.grandartslogic.extentions;
using com.luxza.granlog;

namespace com.luxza.grandartslogic.domain.game.zeroone
{
    public class Referee01 : BaseReferee<Match01, MatchScorer01, Game01EventPublisher>
    {
        internal Referee01(
            Match01 match
        ) : base(match, new MatchScorer01(match))
        {
        }

        public override bool IsReachGameEnd
        {
            get
            {
                //スコアが0のチームがいる。またはラウンド満了かつ投げ切ったならゲーム終了
                //もしくは最大ラウンドを超えてる
                return Participants.AllUnits.Any(unit => Scorer.CurrentScore(unit.Id) == 0) ||
                       Match.IsReachAllTeamsToMaxRound;
            }
        }

        //チェンジした時に呼ばれる
        //最大ラウンドを超えてるならゲーム終了
        public override bool IsNextRoundOverMaxRound => Match.IsNextRoundToOverMaxRound;

        public override void ReStartGame()
        {
            Scorer = new MatchScorer01(Match);
            base.ReStartGame();
        }

        private bool IsCurrentPlayerFinished => Scorer.CurrentScore(CurrentUnit.Id) == 0 || CurrentRoundAtCurrentTeam.GetRoundComponent<IBustController>().IsBust;


        public override int CurrentScore(string unitId)
        {
            return Scorer.CurrentScore(unitId);
        }

        public override void AcceptHit(Segment segment, Vector2? hitPosition = null)
        {
            if (IsCurrentPlayerFinished)
            {
                Log.d("Receive key. but already finished!");
                return;
            }

            var virtualHit = IsSatisfiedInConditions(segment) ? segment : Segment.Miss;
            Match.StoreHit(_currentThrowingUnitIndex, segment, virtualHit, hitPosition);

            var currentScore = Scorer.CurrentScore(CurrentThrowingUnitId);
            if (currentScore < 0)
            {
                Bust();
                return;
            }
            if (currentScore == 0)
            {
                if (!IsSatisfyOutConditions(virtualHit))
                {
                    Bust();
                    return;
                }
            }
            else if (currentScore == 1)
            {
                if (Match.Rule.OutCondition is OutCondition.DoubleOut or OutCondition.MasterOut)
                {
                    Bust();
                    return;
                }
            }

            if (currentScore <= 60)
            {
                //60点以下ならばHighCatchRateとLowCatchRateを確定する。
                //60点のとき、T20を狙うため。
                Scorer.ConfirmHighAndLowCatchRateIfNotConfirmed(CurrentUnit.Id);
            }
            Scorer.CalculateTeamRealTimeStats(CurrentUnit);
            EventPublisher.PublishUpdateProgress(CurrentUnit);
            if (Match.IsReachEndOfRound(_currentThrowingUnitIndex) || CurrentScore(CurrentUnit.Id) == 0)
            {
                var award = AchievedAward(CurrentRoundAtCurrentTeam);
                EventPublisher.PublishAchieveAward(award, CurrentUnit);
                if (IsReachEightyPercent())
                {
                    Confirm80PercentStatsIfNotConfirmed();
                }
                EventPublisher.PublishEndTurn(CurrentUnit);
            }

            UpdateFinishOrder(CurrentScore(CurrentUnit.Id));
            if (IsReachGameEnd)
            {
                GameEnd();
            }
        }

        protected override Award[] AchievedAward(Round round)
        {
            if (CurrentUnit.CurrentThrower.Board == BoardSize.Steel)
                return AchievedAward_Steel(round);
            else
                return base.AchievedAward(round);
        }


        /// <summary>
        /// Steel用のアワードが達成確認をする
        /// overrideしていない理由は現状01のみで使用するアワードなので
        private Award[] AchievedAward_Steel(Round round)
        {
            List<Award> awards = new List<Award>();
            foreach (var award in AchievableAward)
            {
                switch (award)
                {
                    case Award.TonEighty:
                        if (AwardService.IsAchieveTon80(round))
                        {
                            awards.Add(award);
                        }

                        break;
                    case Award.ThreeInTheBlack:
                        if (AwardService.IsAchieve3InTheBlack(round))
                        {
                            awards.Add(award);
                        }

                        break;
                    case Award.ThreeInABed:
                        if (AwardService.IsAchieve3InABed(round))
                        {
                            awards.Add(award);
                        }

                        break;
                    case Award.HatTrick:
                        if (AwardService.IsAchieveHatTrick(round))
                        {
                            awards.Add(award);
                        }

                        break;
                    case Award.OneHundredFortyAndAbove:
                        if (!AwardService.IsAchieveTon80(round) &&
                            !AwardService.IsAchieve3InTheBlack(round) &&
                            !AwardService.IsAchieveHatTrick(round) &&
                            AwardService.IsAchieveOneHundredFortyAndAbove(TotalScore(round)))
                        {
                            awards.Add(award);
                        }

                        break;
                    case Award.OneHundredAndAbove:
                        if (!AwardService.IsAchieveTon80(round) &&
                            !AwardService.IsAchieve3InTheBlack(round) &&
                            !AwardService.IsAchieveHatTrick(round) &&
                            AwardService.IsAchieveOneHundredAndAbove(TotalScore(round)))
                        {
                            awards.Add(award);
                        }

                        break;
                    default:
                        throw new InvalidCodeException($"Invalid award. {award}");
                }
            }

            return awards.ToArray();
        }

        public override void AcceptScore
        (
            int score,
            int checkout,
            int checkoutTry
        )
        {
            if (CurrentUnit.Progress.CurrentRound.TryGetRoundComponent<VisitInput>(out var visitInput))
            {
                visitInput.Input(IsSatisfiedInConditions(score) ? score : 0);
                visitInput.CheckOutNumber = checkout;
                visitInput.CheckOutTryCount = checkoutTry;
            } else {
                throw new InvalidOperationException("Accept score required VisitInput.");
            }


            var currentScore = Scorer.CurrentScore(CurrentThrowingUnitId);
            if (currentScore < 0)
            {
                Bust();
                return;
            }

            if (currentScore == 0)
            {
                if (!IsSatisfyOutConditions(score))
                {
                    Bust();
                    return;
                }
            }

            if (currentScore == 1)
            {
                if (Match.Rule.OutCondition is OutCondition.MasterOut or OutCondition.DoubleOut)
                {
                    Bust();
                    return;
                }
            }
            Scorer.CalculateTeamRealTimeStats(CurrentUnit);
            var award = GranSteelVisitAchievedAward(score);
            EventPublisher.PublishAchieveAward(award, CurrentUnit);
            if (IsReachEightyPercent())
            {
                Confirm80PercentStatsIfNotConfirmed();
            }
            EventPublisher.PublishEndTurn(CurrentUnit);
            EventPublisher.PublishUpdateProgress(CurrentUnit);
            UpdateFinishOrder(CurrentScore(CurrentUnit.Id));

            if (IsReachGameEnd)
            {
                GameEnd();
            }
            else
            {
                // ChangeToNextTeam();
            }
        }

        public override void DiscardCurrentRoundAllInput()
        {
            Scorer.ResetScore(CurrentUnit.Id, CurrentUnitProgress.CurrentRoundNo - 1);
        }

        protected override void ResetRoundData()
        {
            ;
        }

        protected virtual void UpdateFinishOrder(int currentScore)
        {
            return;
        }

        public override void ChangeToNextTeam()
        {
            EndRoundAtCurrentTeam();
            FixRoundAtCurrentUnit();
            if (IsNextRoundOverMaxRound)
            {
                GameEnd();
            }
            else
            {
                if (IsReachEightyPercent())
                {
                    Confirm80PercentStatsIfNotConfirmed();
                }
                //リアルタイムスタッツを再計算
                Scorer.CalculateTeamRealTimeStats(CurrentUnit);
                var sender = CurrentUnit;
                _currentThrowingUnitIndex = NextTeamIndex(_currentThrowingUnitIndex);
                StartRoundAtCurrentTeam();
                EventPublisher.PublishChange(sender);
            }
        }

        protected virtual int NextTeamIndex(int currentTeamIndex)
        {
            return (currentTeamIndex + 1 >= Match.ParticipantTeams.Count) ? 0 : currentTeamIndex + 1;
        }

        protected override void GameEnd()
        {
            //100%に達した時、80%Statsの計算をしていない場合がある。
            //80%を通り越して、100%(ゲーム終了）となってしまう場合を考慮する。
            Confirm80PercentStatsIfNotConfirmed();
            Confirm100PercentStats();
            foreach (var team in Participants.AllUnits)
            {
                //最後に確定が必要な場合がある。60点を下回る前にゲーム終了されたなど。
                Scorer.ConfirmHighAndLowCatchRateIfNotConfirmed(team.Id);
            }

            Scorer.CreatePlaydata();
            MatchFinishStatus matchFinishStatus = CalculateRanking();
            if (Match.IsMedley)
            {
                CreateMedleyResultData();
            }
            EventPublisher.PublishFinishMatch(matchFinishStatus);
        }

        private MedleyLegResult CreateMedleyResultData()
        {
            Dictionary<string, int> TeamScoreDic = new Dictionary<string, int>();
            Dictionary<string, double> MemberPPRDic = new Dictionary<string, double>();
            Dictionary<string, double> TeamPPRDic = new Dictionary<string, double>();
            foreach (var team in Participants.AllUnits)
            {
                TeamScoreDic.Add(team.Id, Scorer.CurrentScore(team.Id));

                double ppr80 = 0.0;
                foreach (var member in team.AllMember)
                {
                    var memberppr80 = Scorer.PPRFor80(member.GranId);
                    if (memberppr80 != null)
                    {
                        ppr80 += (double)memberppr80;
                    }

                    MemberPPRDic.Add(member.GranId, memberppr80 == null ? 0.0f : (double)memberppr80);
                }

                TeamPPRDic.Add(team.Id, ppr80);
            }

            return new MedleyLegResult(
                TeamScoreDic,
                TeamPPRDic,
                MemberPPRDic,
                GetWinner()
            );
        }

        private void Bust()
        {
            if (CurrentRoundAtCurrentTeam.GetRoundComponent<IBustController>().IsBust) return;
            CurrentRoundAtCurrentTeam.GetRoundComponent<IBustController>().Bust();
            Scorer.Bust(CurrentThrowingUnitId, CurrentUnitProgress.CurrentRoundNo - 1);
            EventPublisher.PublishBust();
            EventPublisher.PublishUpdateProgress(CurrentUnit);
            EventPublisher.PublishEndTurn(CurrentUnit);
            if (IsNextRoundOverMaxRound)
            {
                GameEnd();
            }
        }

        protected override MatchFinishStatus CalculateRanking()
        {
            if (Participants.Count == 1) return new MatchFinishStatus(Participants.AllUnits[0]);

            DecideRanking(Participants.AllUnits);

            return new MatchFinishStatus(GetWinner());
        }

        /// <summary>
        /// ランキングを決めます。
        /// 指定したUnitの配列と、何番目から割り振るかを指定します。
        /// </summary>
        /// <param name="units">Unitのリスト</param>
        /// <param name="to">何番目から割り振るか</param>
        protected void DecideRanking(Unit[] units, int to = 1)
        {
            //ゲームの順位決めのロジック
            //2チーム以上いる場合は全てのチームのスコアを低い順に並べ替え
            var ordered = units.OrderBy(team =>
            {
                //1.Score昇順
                var score = CurrentScore(team.Id);
                return score;
            }).ToList();
            //もし全てのUnitのスコアが同じならPPR80で順位決める
            if (ordered.All(team => CurrentScore(team.Id) == CurrentScore(ordered[0].Id)))
            {
                ordered = units.OrderByDescending(team =>
                {
                    //PPRFor80降順
                    var ppr80 = team.AllMember.Average(member => Scorer.PPRFor80(member.GranId));
                    return ppr80;
                }).ToList();
            }
            //並べ替えたリストにgameRankingの値を1~チーム数上限(最大8)の値を降っていく
            int rank = to;
            for (int i = 0; i < ordered.Count(); i++)
            {
                if (i == 0)
                {
                    Participants.Unit(ordered.ElementAt(i).Id).GameRanking = rank;
                    continue;
                }
                var currentTeamId = ordered.ElementAt(i).Id;

                Participants.Unit(currentTeamId).GameRanking = ++rank;
            }
            //もしスコア、スタッツ、全て同点だった場合は
            //専用のドロー識別処理に入り確実に誰かを勝者にする
            if (ordered.All(team => CurrentScore(team.Id) == CurrentScore(ordered[0].Id)) &&
                ordered.All(team => team.AllMember.Average(member => Scorer.PPRFor80(member.GranId)) == ordered[0].AllMember.Average(member => Scorer.PPRFor80(member.GranId))))
            {

                if (Match.Rule.EndScore == CurrentScore(CurrentThrowingUnitId))
                {
                    //ゲーム開始時のスコアと最後のスコアが一致するということは誰も投げてない
                    //その場合はユニットの投げ順を順位にする
                    int rankval = 1;
                    foreach (var unit in Match.ParticipantTeams.AllUnits)
                    {
                        unit.GameRanking = rankval;
                        rankval++;
                    }

                }
                else
                {
                    //もし開始時のスコアが違うのであれば
                    //先にそのスコアに到達したユニット順に並べかえる
                    var drawSortingUnits = Scorer.FindbyScoreUnits
                    (
                        Match.ParticipantTeams.AllUnits,
                        Match.Rule.EndScore,
                        CurrentScore(CurrentThrowingUnitId)
                    );
                    for (var i = 0; i < drawSortingUnits.Length; i++)
                    {
                        Participants.Unit(i).GameRanking = drawSortingUnits[i].GameRanking;
                    }
                }
            }
        }

        public Unit GetWinner() => Participants.AllUnits.First(team => team.GameRanking == 1);

        private void Confirm80PercentStatsIfNotConfirmed()
        {
            foreach (var team in Participants.AllUnits)
            {
                foreach (var player in team.AllMember)
                {
                    if (Scorer.PPRFor80(player.GranId).HasValue || Scorer.PPDFor80(player.GranId).HasValue)
                    {
                        //already confirmed.
                        continue;
                    }

                    var totalRoundScore = 0;
                    var MemberRounds = team.AllRoundsByMember(player.GranId);
                    var totalThrow = 0;
                    foreach (var round in MemberRounds)
                    {
                        totalRoundScore += Scorer.TotalScoreInRound(round);
                        var visitInput = round.GetRoundComponent<VisitInput>();
                        if (visitInput != null)
                        {
                            if (visitInput.CheckOutNumber == 0)
                            {
                                totalThrow += Match.Rule.ThrowsPerRound;
                            }
                            else
                            {
                                totalThrow += visitInput.CheckOutNumber;
                            }
                        }
                        else
                        {
                            totalThrow += round.ThrowCount;
                        }
                    }
                    var ppr = MemberRounds.Length == 0 ? 0.00 : totalRoundScore / (double)MemberRounds.Length;
                    var ppd = MemberRounds.Length == 0 ? 0.00 : totalRoundScore / (double)totalThrow;
                    Scorer.SetPPRFor80(player.GranId, ppr);
                    Scorer.SetPPDFor80(player.GranId, ppd);
                }
            }
        }

        public bool IsAchieveInConditionAtCurrentTeam()
        {
            return IsAchieveInCondition(CurrentUnit);
        }

        private bool IsAchieveInCondition(Unit unit)
        {
            return Match.Rule.InCondition switch
            {
                InCondition.OpenIn => true,
                InCondition.DoubleIn => unit.AllThrows().
                    Any(t => InOutConditionService.IsAchievableDoubleConditionKey(t.VirtualHitArea, Match.Rule.BullOption == BullOption.SeparateBull)),
                InCondition.MasterIn => unit.AllThrows().
                    Any(t => InOutConditionService.IsAchievableMasterConditionKey(t.VirtualHitArea)),
                _ => true
            };
        }

        private bool IsAchieveInCondition(string unitId)
        {
            var unit = Participants.Unit(unitId);
            return IsAchieveInCondition(unit);
        }

        private void Confirm100PercentStats()
        {
            foreach (var team in Participants.AllUnits)
            {
                foreach (var player in team.AllMember)
                {
                    if (Scorer.PPRFor100(player.GranId).HasValue || Scorer.PPDFor100(player.GranId).HasValue)
                    {
                        //already confirmed.
                        continue;
                    }

                    var totalRoundScore = 0;
                    var MemberRounds = team.AllRoundsByMember(player.GranId);

                    var totalThrow = 0;
                    foreach (var round in MemberRounds)
                    {
                        totalRoundScore += Scorer.TotalScoreInRound(round);
                        var visitInput = round.GetRoundComponent<VisitInput>();
                        if (visitInput != null)
                        {
                            if (visitInput.CheckOutNumber == 0)
                            {
                                totalThrow += Match.Rule.ThrowsPerRound;
                            }
                            else
                            {
                                totalThrow += visitInput.CheckOutNumber;
                            }
                        }
                        else
                        {
                            totalThrow += round.ThrowCount;
                        }
                    }

                    var ppr = MemberRounds.Length == 0 ? 0.00 : totalRoundScore / (double)MemberRounds.Length;
                    var ppd = MemberRounds.Length == 0 ? 0.00 : totalRoundScore / (double)totalThrow;
                    Scorer.SetPPRFor100(player.GranId, ppr);
                    Scorer.SetPPDFor100(player.GranId, ppd);
                }
            }
        }

        public bool IsSatisfiedInConditions(Segment segment, string unitId)
        {
            if (!IsAchieveInCondition(unitId))
            {
                switch (Match.Rule.InCondition)
                {
                    case InCondition.DoubleIn: return InOutConditionService.IsAchievableDoubleConditionKey(segment, Match.Rule.BullOption == BullOption.SeparateBull);
                    case InCondition.MasterIn: return InOutConditionService.IsAchievableMasterConditionKey(segment);
                }
            }

            return true;
        }

        /// <summary>
        /// 開始条件のチェックをします。
        /// <see cref="GameStartConditions"/>
        /// </summary>
        /// <param name="segment">刺さった位置</param>
        /// <returns>刺さった位置が開始条件を満たすかどうか</returns>
        private bool IsSatisfiedInConditions(Segment segment)
        {
            if (!IsAchieveInConditionAtCurrentTeam())
            {
                switch (Match.Rule.InCondition)
                {
                    case InCondition.DoubleIn: return InOutConditionService.IsAchievableDoubleConditionKey(segment, Match.Rule.BullOption == BullOption.SeparateBull);
                    case InCondition.MasterIn: return InOutConditionService.IsAchievableMasterConditionKey(segment);
                }
            }

            return true;
        }

        private bool IsSatisfiedInConditions
        (
            int score
        )
        {
            if (Scorer.CurrentSubtractedScore(CurrentUnit.Id) == 0)
            {
                return Match.Rule.InCondition switch
                {
                    InCondition.OpenIn => true,
                    InCondition.DoubleIn => InOutConditionService.AchievableDoubleInOrOutScore(score),
                    InCondition.MasterIn => InOutConditionService.AchievableMasterInOrOutScore(score),
                    _ => true
                };
            }

            return true;
        }

        /// <summary>
        /// 指定したSegmentが終了条件を満たしているかを返却します。
        /// </summary>
        /// <param name="segment">Hitしたセグメント</param>
        /// <returns>
        /// 終了条件が設定されている場合にその条件を満たすHitかどうかを返却します。
        /// </returns>
        public bool IsSatisfyOutConditions(Segment segment)
        {
            return Match.Rule.OutCondition switch
            {
                OutCondition.DoubleOut => InOutConditionService.IsAchievableDoubleConditionKey
                    (segment, Match.Rule.BullOption == BullOption.SeparateBull),
                OutCondition.MasterOut => InOutConditionService.IsAchievableMasterConditionKey(segment),
                _ => true
            };
        }


        private bool IsSatisfyOutConditions(int score)
        {
            return Match.Rule.OutCondition switch
            {
                OutCondition.DoubleOut => InOutConditionService.AchievableDoubleInOrOutScore(score),
                OutCondition.MasterOut => InOutConditionService.AchievableMasterInOrOutScore(score),
                _ => true
            };
        }

        public override int TotalScoreAtCurrentThrowingTeam => Scorer.CurrentScore(CurrentThrowingUnitId);

        public override IEnumerable<(Round round, int score)> RoundsAndScoresAt
        (
            string unitId
        )
        {
            return AllRoundsAt(unitId).Select(r => (r, Scorer.TotalScoreInRound(r)));
        }

        public override void RefreshGameData()
        {
            //80%に満たない場合、Statsもリセットする
            if (!IsReachEightyPercent())
            {
                Scorer.DiscardStats();
            }

            //リアルタイムスタッツを再計算
            Scorer.CalculateTeamRealTimeStats(CurrentUnit);
        }


        public override Award[] AchievableAward
        {
            get
            {
                if (CurrentUnit.CurrentThrower.Board == BoardSize.Steel) return GameRule01.AchievableAwards_Steel;
                return GameRule01.AchievableAwards;
            }
        }

        //01Gameではダブルイン、マスターインクリアしてるフラグを戻してからラウンドリバースの通知流す
        public override void RevertCurrentRound()
        {

            if (CurrentUnitProgress.CurrentRound.No == 1 && _currentThrowingUnitIndex == 0 &&
                 CurrentUnitProgress.CurrentRound.ThrowCount <= 0)
            {
                //1ラウンドの最初のチームが1投もしてない時にラウンドリバースした時は処理自体をしないn
                return;
            }

            try
            {

                EventPublisher.Enabled = false;
                var sender = CurrentUnit;
                var changedThrowUnit = false;
                //もし現在のチームが1投もしてなければ
                //前のチームに戻す
                if (CurrentUnitProgress.CurrentRound.ThrowCount <= 0)
                {
                    var roundNo = CurrentRoundAtCurrentTeam.No;
                    //前のチームに戻るので現在のRoundを削除
                    CurrentUnitProgress.DiscardCurrentRound();
                    _currentThrowingUnitIndex = RevertUnitIndex(_currentThrowingUnitIndex, roundNo);
                    changedThrowUnit = true;
                }
                ////現在のラウンドのデータをリセットして通知
                CurrentUnitProgress.ResetCurrentRound();

                //現在のラウンドの最新のスローを一つリセット
                CurrentUnitProgress.ResetCurrentRoundThrow();

                ResetRoundData();
                RefreshGameData();
                EventPublisher.Enabled = true;
                EventPublisher.PublishRoundReverse(sender);
                if (changedThrowUnit)
                {
                    //もしチームが変わったならば、チーム変更を通知する
                    EventPublisher.PublishChange(sender);
                }
            }
            finally
            {
                EventPublisher.Enabled = true;
            }
        }

        /// <summary>
        /// 前のチームインデックスに戻す
        /// 1引いた数値を入れ直す。最初のチームだった場合は最後のチームのインデックスにする。
        /// </summary>
        /// <param name="currentIndex"></param>
        /// <param name="targetRoundNo"></param>
        /// <returns></returns>
        protected virtual int RevertUnitIndex(int currentIndex, int targetRoundNo)
        {
            return (currentIndex - 1 < 0)
                ? Match.ParticipantTeams.Count - 1
                : currentIndex - 1;
        }

        private bool IsReachEightyPercent()
        {
            return Participants.AllUnits.Any(team => Match.Rule.EightyPercentScore >= CurrentScore(team.Id));
        }
    }
}